<!--  --><!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- 标题会动态变化 -->
    <title>IMAX 中国影院分布</title>
    <meta name="description" content="探索中国各地的 IMAX 影院位置和技术信息，包含激光与数字 IMAX 影院的完整数据。">
    
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <style>
        /* 引入一个更具科技感的字体 (可选，但推荐) */
        @import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@700&family=Roboto:wght@400;500;700&display=swap');

        /* 全局自定义样式 */
        body {
            background-color: #111827;
            background-image: radial-gradient(ellipse at top, #1f2937, #111827 70%);
            font-family: 'Roboto', sans-serif;
            overflow-x: hidden; /* 防止横向滚动 */
        }

        /* 增强的标题样式 */
        .header-title {
            font-family: 'Orbitron', sans-serif;
            text-shadow: 0 0 8px rgba(129, 140, 248, 0.4), 0 0 12px rgba(167, 139, 250, 0.3);
            transition: all 0.4s ease-in-out;
            cursor: pointer; /* 暗示可点击 */
        }
        
        .header-title:hover {
            filter: brightness(1.2);
            transform: scale(1.02);
        }

        /* --- START: 霓虹灯效果重大更新 --- */
        .header-title.neon-active {
            /* 激活时，覆盖渐变背景，让文字本身显示出来 */
            background-image: none;
            -webkit-text-fill-color: white; /* 设置文字核心为白色 */
            animation: sharpNeonPulse 2s ease-in-out;
        }

        @keyframes sharpNeonPulse {
            /* 动画的中间状态，即最亮的时候 */
            50% {
                text-shadow:
                    /* 关键：多层白色辉光，营造锐利、亮白的灯管核心 */
                    0 0 5px #fff,
                    0 0 10px #fff,
                    0 0 15px #fff,
                    /* 外围的彩色光晕 */
                    0 0 25px #a78bfa,
                    0 0 45px #a78bfa,
                    0 0 70px #818cf8;
            }
            /* 动画开始和结束时，恢复到较暗状态，但仍保持激活样式 */
            0%, 100% {
                 text-shadow:
                    0 0 2px #fff,
                    0 0 5px #a78bfa,
                    0 0 10px #818cf8;
            }
        }
        /* --- END: 霓虹灯效果重大更新 --- */
        
        header {
            border-bottom: 1px solid;
            border-image-source: linear-gradient(to right, transparent, #4f46e5 50%, transparent);
            border-image-slice: 1;
        }

        /* --- START: 新增模式切换器样式 --- */
        .mode-switcher {
            display: inline-flex;
            background-color: rgba(31, 41, 55, 0.7);
            border-radius: 0.5rem;
            padding: 0.25rem;
            border: 1px solid #374151;
        }
        .mode-btn {
            padding: 0.5rem 1.25rem;
            border-radius: 0.375rem;
            font-size: 0.875rem;
            font-weight: 500;
            color: #9ca3af;
            border: none;
            background-color: transparent;
            cursor: pointer;
            transition: all 0.3s ease;
            white-space: nowrap;
        }
        .mode-btn:hover {
            background-color: rgba(55, 65, 81, 0.5);
            color: #d1d5db;
        }
        .mode-btn.active {
            background-color: #4f46e5;
            color: #fff;
            box-shadow: 0 2px 10px rgba(79, 70, 229, 0.4);
        }
        /* --- END: 新增模式切换器样式 --- */

        .cinema-card {
            animation: fadeIn 0.5s ease-out forwards;
            opacity: 0;
        }
        @keyframes fadeIn {
            to { opacity: 1; }
        }

        .cinema-card {
            background-color: rgba(31, 41, 55, 0.5);
            backdrop-filter: blur(4px);
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            border: 1px solid #374151;
            transition: all 0.3s ease;
            position: relative;
            overflow: visible;
        }
        .cinema-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(139, 92, 246, 0.1), transparent);
            transition: left 0.5s ease;
        }

        /* 移动端禁用hover动画，避免横向滚动 */
        @media (hover: hover) and (pointer: fine) {
            .cinema-card:hover {
                border-color: #6366f1;
                transform: translateY(-3px);
                box-shadow: 0 0 20px rgba(99, 102, 241, 0.2);
            }
            .cinema-card:hover::before {
                left: 100%;
            }
        }

        .cinema-name { font-size: 1.125rem; font-weight: 600; color: #f9fafb; margin-bottom: 0.75rem; }
        .card-row { display: flex; justify-content: space-between; align-items: center; padding: 0.625rem 0; border-bottom: 1px solid #374151; }
        .card-row:last-child { border-bottom: none; }
        .card-label { font-size: 0.875rem; color: #9ca3af; }
        .card-value { font-size: 0.875rem; color: #e5e7eb; font-weight: 500; }
        .cinema-type-badge { display: inline-block; padding: 0.375rem 0.75rem; border-radius: 9999px; font-size: 0.75rem; font-weight: 600; border: 1px solid transparent; background-clip: padding-box; }
        
        .audio-feature { position: relative; display: inline-flex; align-items: center; cursor: help; user-select: none; -webkit-user-select: none; -ms-user-select: none; }
        .audio-feature .tooltip-text { visibility: hidden; white-space: nowrap; background-color: #111827; color: #fff; text-align: center; border-radius: 6px; padding: 8px 12px; position: absolute; z-index: 9999; bottom: 150%; left: 50%; transform: translateX(-50%); opacity: 0; transition: opacity 0.3s; font-size: 0.75rem; pointer-events: none; box-shadow: 0 4px 12px rgba(0,0,0,0.6); border: 1px solid #4b5563; }
        .audio-feature:hover .tooltip-text { visibility: visible; opacity: 1; }

        .stat-card {
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid #374151;
            background-color: rgba(31, 41, 55, 0.5);
            backdrop-filter: blur(4px);
            user-select: none;
            -webkit-user-select: none;
            -ms-user-select: none;
        }
        .stat-card:hover {
            border-color: #4f46e5;
            box-shadow: 0 0 15px rgba(79, 70, 229, 0.3);
        }
        .stat-card.active-filter {
            border-color: #818cf8;
            box-shadow: 0 0 20px rgba(129, 140, 248, 0.4);
        }

        /* 搜索框聚焦效果 */
        .focus\:shadow-indigo:focus {
            box-shadow: 0 0 15px rgba(79, 70, 229, 0.3);
        }
        
        @media (min-width: 768px) {
            #cinema-list-header { 
                display: grid; 
                grid-template-columns: 3fr 2fr 1fr 1fr 1fr 1fr; 
                gap: 1rem; 
                padding: 1rem 1.5rem; 
                background-color: transparent;
                font-weight: 600; 
                color: #9ca3af; 
                border-bottom: 2px solid #374151; 
            }
            .cinema-card { 
                display: grid; 
                grid-template-columns: 3fr 2fr 1fr 1fr 1fr 1fr; 
                gap: 1rem; 
                align-items: center; 
                padding: 1.25rem 1.5rem;
                margin-bottom: 0; 
                border-radius: 0; 
                border: none;
                border-bottom: 1px solid #374151; 
                box-shadow: none; 
                background-color: transparent;
                backdrop-filter: none;
                border-left: 3px solid transparent;
            }
            .cinema-card::before { display: none; }
            .cinema-card:hover { 
                background-color: #37415130; 
                transform: none; 
                box-shadow: none;
                border-left-color: #6366f1;
            }
            .cinema-card:last-child { border-bottom: none; }
            .card-row { display: contents; }
            .card-label { display: none; }
            .card-value { color: #d1d5db; }
            .cinema-name { margin-bottom: 0; }
            .sortable { cursor: pointer; transition: color 0.2s ease; display: flex; align-items: center; user-select: none; -webkit-user-select: none; -ms-user-select: none; }
            .sortable:hover, .sortable.active { color: #f9fafb; }
            .sort-icon-container { margin-left: 0.5rem; display: flex; align-items: center; height: 16px; width: 16px; }
            .sort-icon { 
                transition: all 0.3s ease; 
                color: #6366f1;
                opacity: 0;
                transform: translateY(3px);
            }
            .sortable.active .sort-icon-asc:not(.hidden),
            .sortable.active .sort-icon-desc:not(.hidden) {
                 opacity: 1;
                 transform: translateY(0);
            }
        }

        /* 加载动画 */
        .loading-spinner {
            border: 4px solid #374151;
            border-top: 4px solid #6366f1;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body class="text-gray-300 min-h-screen">
    <!-- 页面头部 -->
    <header class="bg-transparent text-white text-center py-12 md:py-16">
        <div class="max-w-4xl mx-auto px-4">
            <h1 id="page-title" class="header-title text-4xl md:text-5xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-indigo-400 to-purple-400">
                激光 IMAX 影院分布
            </h1>
            <p class="text-lg opacity-80 mb-6">寻找当地最佳影院，探索极致观影体验</p>
            
            <!-- 新增：模式切换器 -->
            <div class="mode-switcher">
                <button id="btn-laser" class="mode-btn active">激光 IMAX</button>
                <button id="btn-digital" class="mode-btn">数字 IMAX</button>
            </div>
        </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="max-w-6xl mx-auto px-4 py-8">
        <!-- 搜索框 -->
        <div class="mb-8 relative">
            <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                <svg class="h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z" clip-rule="evenodd" />
                </svg>
            </div>
            <input
                type="text"
                id="searchInput"
                class="w-full py-3 pl-12 pr-12 text-white bg-gray-800 border border-gray-700 rounded-lg placeholder-gray-500 focus:outline-none focus:border-indigo-500 focus:shadow-indigo transition-all duration-300"
                placeholder="按城市、影院名称搜索..."
            >
            <button
                id="clearSearchBtn"
                class="absolute inset-y-0 right-0 pr-4 flex items-center text-gray-500 hover:text-gray-300 transition-colors duration-200 hidden"
                type="button"
            >
                <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M6.28 5.22a.75.75 0 00-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 101.06 1.06L10 11.06l3.72 3.72a.75.75 0 101.06-1.06L11.06 10l3.72-3.72a.75.75 0 00-1.06-1.06L10 8.94 6.28 5.22z" />
                </svg>
            </button>
        </div>

        <!-- 统计信息 -->
        <div id="statsSection" class="my-6 grid grid-cols-2 md:grid-cols-4 gap-4">
            <!-- 统计数据将通过 JavaScript 注入 -->
        </div>

        <!-- 表格/卡片列表区域 -->
        <div class="md:bg-gray-800/50 md:rounded-lg md:border md:border-gray-700">
            <!-- 桌面端表头 -->
            <div id="cinema-list-header" class="hidden md:grid">
                <span class="sortable" data-sort="name">影城名称<span class="sort-icon-container"><svg class="sort-icon sort-icon-asc hidden" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16"><path fill-rule="evenodd" d="M8 15a.5.5 0 0 0 .5-.5V2.707l3.146 3.147a.5.5 0 0 0 .708-.708l-4-4a.5.5 0 0 0-.708 0l-4 4a.5.5 0 1 0 .708.708L7.5 2.707V14.5a.5.5 0 0 0 .5.5"/></svg><svg class="sort-icon sort-icon-desc hidden" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16"><path fill-rule="evenodd" d="M8 1a.5.5 0 0 1 .5.5v11.793l3.146-3.147a.5.5 0 0 1 .708.708l-4 4a.5.5 0 0 1-.708 0l-4-4a.5.5 0 0 1 .708-.708L7.5 13.293V1.5A.5.5 0 0 1 8 1"/></svg></span></span>
                <span class="sortable" data-sort="projectorType">放映机型号<span class="sort-icon-container"></span></span>
                <span class="sortable" data-sort="screenWidth">宽(m)<span class="sort-icon-container"></span></span>
                <span class="sortable" data-sort="screenHeight">高(m)<span class="sort-icon-container"></span></span>
                <span class="sortable" data-sort="screenArea">面积(㎡)<span class="sort-icon-container"></span></span>
                <span class="sortable" data-sort="seatCount">座位数<span class="sort-icon-container"></span></span>
            </div>
            
            <!-- 数据容器 -->
            <div id="cinema-list"></div>
            <p id="no-results" class="text-center py-16 text-gray-500 hidden">未找到匹配的影院。</p>
        </div>
    </main>

    <!-- 页脚 -->
    <footer class="text-white py-8 mt-4">
        <div class="max-w-6xl mx-auto px-4 text-center">
            <p class="text-gray-400 text-sm">
                数据来源：<a href="https://docs.qq.com/sheet/DQ3FEUUZJdklNSWJP?tab=BB08J2" target="_blank" rel="noopener noreferrer" class="text-indigo-400 hover:text-indigo-300 underline decoration-dotted underline-offset-4">@ArvinTingcn</a>
            </p>
        </div>
    </footer>

    <script>
        // --- 全局变量与状态管理 ---
        let originalData = [];
        let currentSort = { field: null, direction: null };
        let activeFilter = 'all';
        let currentMode = 'laser'; // 'laser' or 'digital'
        const dataCache = { laser: null, digital: null }; // 缓存已加载的数据

        // --- DOM 元素引用 ---
        const listContainer = document.getElementById('cinema-list');
        const searchInput = document.getElementById('searchInput');
        const clearSearchBtn = document.getElementById('clearSearchBtn');
        const noResults = document.getElementById('no-results');
        const pageTitle = document.getElementById('page-title');
        const statsSection = document.getElementById('statsSection');
        const btnLaser = document.getElementById('btn-laser');
        const btnDigital = document.getElementById('btn-digital');

        // --- 核心功能函数 ---

        // 显示加载状态
        function showLoading(message) {
            listContainer.innerHTML = `<div class="text-center py-10"><div class="loading-spinner mx-auto mb-4"></div><p class="text-gray-400">${message}</p></div>`;
            statsSection.innerHTML = ''; // 清空旧统计数据
            noResults.classList.add('hidden');
        }

        // 加载JSON数据（动态）
        async function loadCinemaData(mode) {
            const filename = mode === 'laser' ? 'imax_cinemas.json' : 'imax_digital_cinemas.json';
            // 如果数据已在缓存中，直接返回
            if (dataCache[mode]) {
                console.log(`✅ 从缓存加载 ${mode} 模式数据`);
                return dataCache[mode];
            }
            try {
                const response = await fetch(filename);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                const data = await response.json();
                console.log(`✅ 成功加载 ${data.length} 条来自 ${filename} 的数据`);
                dataCache[mode] = data; // 缓存数据
                return data;
            } catch (error) {
                console.error(`❌ 加载 ${filename} 数据失败:`, error);
                listContainer.innerHTML = `<div class="text-center py-10"><p class="text-red-400">错误：无法加载影院数据 (${filename})。<br>请确保文件存在且格式正确。</p></div>`;
                return []; // 返回空数组作为后备
            }
        }

        // 格式化数字
        function formatNumber(value) {
            if (!value || value === '--' || value === '') return '--';
            const num = parseFloat(value);
            return isNaN(num) ? '--' : num.toFixed(2);
        }

        // 渲染影院列表
        function renderList(data) {
            listContainer.innerHTML = '';
            if (data.length === 0) {
                noResults.classList.remove('hidden');
                return;
            }
            noResults.classList.add('hidden');

            data.forEach((cinema, index) => {
                const type = cinema.projectorType || '';
                let badgeStyle = 'bg-gray-700 text-gray-300 border-gray-600'; // 默认/数字
                if (currentMode === 'laser') {
                    if (type.includes('Commercial')) badgeStyle = 'bg-blue-900/50 text-blue-300 border-blue-500/30';
                    else if (type.includes('Laser XT')) badgeStyle = 'bg-green-900/50 text-green-300 border-green-500/30';
                    else if (type.includes('GT')) badgeStyle = 'bg-purple-900/50 text-purple-300 border-purple-500/30';
                }

                const audioFeatureHTML = (cinema.audioSystem && cinema.audioSystem.includes('12声道'))
                    ? `<div class="audio-feature"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="text-gray-400 hover:text-white transition" viewBox="0 0 16 16"><path d="M11.536 14.01A8.47 8.47 0 0 0 14.026 8a8.47 8.47 0 0 0-2.49-6.01l-.708.707A7.48 7.48 0 0 1 13.025 8c0 2.071-.84 3.946-2.197 5.303z"/><path d="M10.121 12.596A6.48 6.48 0 0 0 12.025 8a6.48 6.48 0 0 0-1.904-4.596l-.707.707A5.48 5.48 0 0 1 11.025 8a5.48 5.48 0 0 1-1.61 3.89z"/><path d="M8.707 11.182A4.5 4.5 0 0 0 10.025 8a4.5 4.5 0 0 0-1.318-3.182L8 5.525A3.5 3.5 0 0 1 9.025 8 3.5 3.5 0 0 1 8 10.475zM6.717 3.55A.5.5 0 0 1 7 4v8a.5.5 0 0 1-.812.39L3.825 10.5H1.5A.5.5 0 0 1 1 10V6a.5.5 0 0 1 .5-.5h2.325l2.363-1.89a.5.5 0 0 1 .529-.06z"/></svg><span class="tooltip-text">${cinema.audioSystem}</span></div>` : '';

                const cardHTML = `<div class="cinema-card" style="animation-delay: ${index * 50}ms;"><div class="cinema-name">${cinema.name}</div><div class="card-value flex items-center space-x-2"><span class="cinema-type-badge ${badgeStyle}">${cinema.projectorType}</span>${audioFeatureHTML}</div><div class="card-row md:contents"><span class="card-label">宽(m)</span><span class="card-value">${formatNumber(cinema.screenWidth)}</span></div><div class="card-row md:contents"><span class="card-label">高(m)</span><span class="card-value">${formatNumber(cinema.screenHeight)}</span></div><div class="card-row md:contents"><span class="card-label">面积(㎡)</span><span class="card-value">${formatNumber(cinema.screenArea)}</span></div><div class="card-row md:contents"><span class="card-label">座位数</span><span class="card-value">${cinema.seatCount || '--'}</span></div></div>`;
                listContainer.insertAdjacentHTML('beforeend', cardHTML);
            });
        }

        // --- 更新统计数据（动态） ---
        function updateStats(data) {
            let statsHTML = '';
            if (currentMode === 'laser') {
                const commercial = data.filter(c => (c.projectorType || '').includes('Commercial')).length;
                const laserXT = data.filter(c => (c.projectorType || '').includes('Laser XT')).length;
                const gt3d = data.filter(c => (c.projectorType || '').includes('GT')).length;
                statsHTML = `
                    <div class="stat-card p-4 rounded-xl text-center" data-filter="all"><div class="text-3xl font-bold text-indigo-400">${data.length}</div><div class="text-gray-400 mt-1 text-sm">总影院数</div></div>
                    <div class="stat-card p-4 rounded-xl text-center" data-filter="GT"><div class="text-3xl font-bold text-purple-400">${gt3d}</div><div class="text-gray-400 mt-1 text-sm">一代 GT</div></div>
                    <div class="stat-card p-4 rounded-xl text-center" data-filter="Commercial"><div class="text-3xl font-bold text-blue-400">${commercial}</div><div class="text-gray-400 mt-1 text-sm">二代 Cola</div></div>
                    <div class="stat-card p-4 rounded-xl text-center" data-filter="Laser XT"><div class="text-3xl font-bold text-green-400">${laserXT}</div><div class="text-gray-400 mt-1 text-sm">三代 XT</div></div>
                `;
            } else { // digital mode - NEW LOGIC
                const count_gt_300 = data.filter(c => (parseFloat(c.screenArea) || 0) > 300).length;
                const count_200_300 = data.filter(c => { const area = parseFloat(c.screenArea) || 0; return area > 200 && area <= 300; }).length;
                const count_lt_200 = data.filter(c => (parseFloat(c.screenArea) || 0) <= 200 && (parseFloat(c.screenArea) || 0) > 0).length;

                statsHTML = `
                    <div class="stat-card p-4 rounded-xl text-center" data-filter="all"><div class="text-3xl font-bold text-indigo-400">${data.length}</div><div class="text-gray-400 mt-1 text-sm">总影院数</div></div>
                    <div class="stat-card p-4 rounded-xl text-center" data-filter="gt_300"><div class="text-3xl font-bold text-red-400">${count_gt_300}</div><div class="text-gray-400 mt-1 text-sm">巨型厅 (>300㎡)</div></div>
                    <div class="stat-card p-4 rounded-xl text-center" data-filter="200_300"><div class="text-3xl font-bold text-orange-400">${count_200_300}</div><div class="text-gray-400 mt-1 text-sm">大型厅 (200-300㎡)</div></div>
                    <div class="stat-card p-4 rounded-xl text-center" data-filter="lt_200"><div class="text-3xl font-bold text-yellow-400">${count_lt_200}</div><div class="text-gray-400 mt-1 text-sm">标准厅 (<200㎡)</div></div>
                `;
            }
            statsSection.innerHTML = statsHTML;
            updateActiveFilterUI();
        }

        // --- 排序与筛选 ---

        function updateActiveFilterUI() {
            document.querySelectorAll('.stat-card').forEach(card => {
                card.classList.toggle('active-filter', card.dataset.filter === activeFilter);
            });
        }
        
        function sortData(data, field, direction) {
            return [...data].sort((a, b) => {
                let aVal = a[field], bVal = b[field];
                if (['screenWidth', 'screenHeight', 'screenArea', 'seatCount'].includes(field)) { aVal = parseFloat(aVal) || -1; bVal = parseFloat(bVal) || -1; } 
                else { aVal = String(aVal || '').toLowerCase(); bVal = String(bVal || '').toLowerCase(); }
                if (direction === 'asc') return aVal > bVal ? 1 : -1;
                return aVal < bVal ? 1 : -1;
            });
        }

        function updateSortIcons(field, direction) {
            document.querySelectorAll('.sortable').forEach(header => {
                const ascIcon = header.querySelector('.sort-icon-asc'), descIcon = header.querySelector('.sort-icon-desc');
                if (!ascIcon || !descIcon) return;
                header.classList.remove('active');
                ascIcon.classList.add('hidden'); descIcon.classList.add('hidden');
                if (header.dataset.sort === field && direction) {
                    header.classList.add('active');
                    if (direction === 'asc') ascIcon.classList.remove('hidden'); else descIcon.classList.remove('hidden');
                }
            });
        }
        
        // --- 核心渲染与过滤流程 ---
        
        function filterAndRender() {
            let baseData = [...originalData];

            // 1. 搜索过滤
            const searchTerm = searchInput.value.toLowerCase();
            if (searchTerm) {
                baseData = baseData.filter(c => (c.name || '').toLowerCase().includes(searchTerm));
            }

            // 2. 更新统计数据 (基于搜索后的结果)
            updateStats(baseData);
            
            // 3. 分类过滤
            let dataToRender = [...baseData];
            if (activeFilter !== 'all') {
                if (currentMode === 'laser') {
                    dataToRender = dataToRender.filter(c => (c.projectorType || '').includes(activeFilter));
                } else { // digital mode filters - NEW LOGIC
                    dataToRender = dataToRender.filter(c => {
                        const area = parseFloat(c.screenArea) || 0;
                        switch (activeFilter) {
                            case 'gt_300': return area > 300;
                            case '200_300': return area > 200 && area <= 300;
                            case 'lt_200': return area <= 200 && area > 0;
                            default: return false;
                        }
                    });
                }
            }
            
            // 4. 排序
            if (currentSort.direction) {
                dataToRender = sortData(dataToRender, currentSort.field, currentSort.direction);
            }
            
            // 5. 渲染列表
            renderList(dataToRender);
            updateSortIcons(currentSort.field, currentSort.direction);
        }
        
        // --- 模式切换主函数 ---

        async function switchModeAndRender(newMode) {
            if (newMode === currentMode && dataCache[newMode]) return; // 如果模式未变且数据已加载，则不执行
            
            currentMode = newMode;
            
            // 1. 更新UI状态
            const titleText = newMode === 'laser' ? '激光 IMAX 影院分布' : '数字 IMAX 影院分布';
            pageTitle.textContent = titleText;
            document.title = titleText;
            btnLaser.classList.toggle('active', newMode === 'laser');
            btnDigital.classList.toggle('active', newMode === 'digital');
            showLoading(`正在加载 ${newMode === 'laser' ? '激光' : '数字'} IMAX 数据...`);

            // 2. 重置状态
            resetState();

            // 3. 加载新数据
            originalData = await loadCinemaData(newMode);
            
            // 4. 初始渲染
            filterAndRender();
        }

        // --- 状态重置与辅助函数 ---
        
        function resetState() {
            searchInput.value = '';
            updateClearButton();
            currentSort = { field: null, direction: null };
            activeFilter = 'all';
        }

        function updateClearButton() {
            clearSearchBtn.classList.toggle('hidden', !searchInput.value.trim());
        }

        function clearSearch() {
            searchInput.value = '';
            updateClearButton();
            filterAndRender();
            searchInput.focus();
        }


        // --- 事件监听器 ---

        document.addEventListener('DOMContentLoaded', async () => {
            // 初始化排序图标
            const sortIconsHTML = document.querySelector('.sort-icon-container').innerHTML;
            document.querySelectorAll('.sortable .sort-icon-container').forEach(c => { if (!c.innerHTML.trim()) c.innerHTML = sortIconsHTML; });

            // 标题点击彩蛋
            pageTitle.addEventListener('click', () => {
                if (!pageTitle.classList.contains('neon-active')) {
                    pageTitle.classList.add('neon-active');
                    setTimeout(() => pageTitle.classList.remove('neon-active'), 2000);
                }
            });

            // 模式切换按钮
            btnLaser.addEventListener('click', () => switchModeAndRender('laser'));
            btnDigital.addEventListener('click', () => switchModeAndRender('digital'));

            // 搜索框
            searchInput.addEventListener('input', () => { updateClearButton(); filterAndRender(); });
            clearSearchBtn.addEventListener('click', clearSearch);
            
            // 统计卡片过滤
            statsSection.addEventListener('click', e => {
                const card = e.target.closest('.stat-card');
                if (!card) return;
                const filterValue = card.dataset.filter;
                activeFilter = activeFilter === filterValue ? 'all' : filterValue;
                filterAndRender();
            });

            // 表头排序
            document.querySelectorAll('.sortable').forEach(header => {
                header.addEventListener('click', () => {
                    const field = header.dataset.sort;
                    if (currentSort.field !== field) { currentSort.field = field; currentSort.direction = 'desc'; } 
                    else if (currentSort.direction === 'desc') { currentSort.direction = 'asc'; } 
                    else { currentSort.direction = null; }
                    filterAndRender();
                });
            });

            // 首次加载，默认进入激光模式
            await switchModeAndRender('laser');
        });
    </script>
</body>
</html>