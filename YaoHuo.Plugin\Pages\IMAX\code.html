<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>激光 IMAX 影院分布 DEMO</title>
    <meta name="description" content="寻找国内最佳 IMAX 影院，探索极致观影体验">
    <meta name="keywords" content="IMAX影院,IMAX影院列表,IMAX中国,IMAX中国影院,IMAX特效厅,激光IMAX,数字IMAX,IMAX影院查询,IMAX Laser,IMAX GT,Commercial Laser,Laser XT,IMAX氙灯"/>
    <!-- Tailwind CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body { background-color: #111827; background-image: radial-gradient(ellipse at top, #1f2937, #111827 70%); font-family: 'Roboto', sans-serif; overflow-x: hidden; }
        .header-title { font-family: 'Orbitron', sans-serif; text-shadow: 0 0 8px rgba(129, 140, 248, 0.4), 0 0 12px rgba(167, 139, 250, 0.3); transition: all 0.4s ease-in-out; cursor: pointer; }
        .imax-text { font-size: 1.06em; margin: 0 6px; }
        .header-title:hover {
            filter: brightness(1.2);
            transform: scale(1.02);
            animation: subtleNeonBreath 2.5s infinite ease-in-out;
        }
        .header-title.neon-active { background-image: none; -webkit-text-fill-color: white; animation: sharpNeonPulse 2s ease-in-out; }
        @keyframes subtleNeonBreath {
            0%, 100% { text-shadow: 0 0 8px rgba(129, 140, 248, 0.4), 0 0 12px rgba(167, 139, 250, 0.3); }
            50% { text-shadow: 0 0 12px rgba(129, 140, 248, 0.6), 0 0 18px rgba(167, 139, 250, 0.5), 0 0 2px rgba(255,255,255,0.5); }
        }
        @keyframes sharpNeonPulse { 50% { text-shadow: 0 0 5px #fff, 0 0 10px #fff, 0 0 15px #fff, 0 0 25px #a78bfa, 0 0 45px #a78bfa, 0 0 70px #818cf8; } 0%, 100% { text-shadow: 0 0 2px #fff, 0 0 5px #a78bfa, 0 0 10px #818cf8; } }
        @media (max-width: 768px) { .header-title.neon-active { animation: mobileNeonPulse 2s ease-in-out; } @keyframes mobileNeonPulse { 50% { text-shadow: 0 0 8px #fff, 0 0 15px #a78bfa; } 0%, 100% { text-shadow: 0 0 3px #fff, 0 0 8px #a78bfa; } } }
        header { border-bottom: 1px solid; border-image-source: linear-gradient(to right, transparent, #4f46e5 50%, transparent); border-image-slice: 1; }
        .mode-switcher { display: inline-flex; background-color: rgba(31, 41, 55, 0.7); border-radius: 0.5rem; padding: 0.25rem; border: 1px solid #374151; }
        .mode-btn { padding: 0.5rem 1.25rem; border-radius: 0.375rem; font-size: 0.875rem; font-weight: 500; color: #9ca3af; border: none; background-color: transparent; cursor: pointer; transition: all 0.3s ease; white-space: nowrap; }
        .mode-btn:hover { background-color: rgba(55, 65, 81, 0.5); color: #d1d5db; }
        .mode-btn.active { background-color: #4f46e5; color: #fff; box-shadow: 0 2px 10px rgba(79, 70, 229, 0.4); }
        .cinema-card { background-color: rgba(31, 41, 55, 0.5); backdrop-filter: blur(4px); border-radius: 12px; padding: 1.5rem; margin-bottom: 1rem; border: 1px solid #374151; transition: all 0.3s ease; position: relative; overflow: visible; }
        .cinema-card::before { content: ''; position: absolute; top: 0; left: -100%; width: 100%; height: 100%; background: linear-gradient(90deg, transparent, rgba(139, 92, 246, 0.1), transparent); transition: left 0.5s ease; }
        @media (hover: hover) and (pointer: fine) { .cinema-card:hover { border-color: #6366f1; transform: translateY(-3px); box-shadow: 0 0 20px rgba(99, 102, 241, 0.2); } .cinema-card:hover::before { left: 100%; } }
        .cinema-name { font-size: 1.125rem; font-weight: 600; color: #f9fafb; margin-bottom: 0.75rem; }
        .card-row { display: flex; justify-content: space-between; align-items: center; padding: 0.625rem 0; border-bottom: 1px solid #374151; }
        .card-row:last-child { border-bottom: none; }
        .card-label { font-size: 0.875rem; color: #9ca3af; }
        .card-value { font-size: 0.875rem; color: #e5e7eb; font-weight: 500; }
        .cinema-type-badge { display: inline-block; padding: 0.375rem 0.75rem; border-radius: 9999px; font-size: 0.75rem; font-weight: 600; border: 1px solid transparent; background-clip: padding-box; }
        .audio-feature { position: relative; display: inline-flex; align-items: center; cursor: help; user-select: none; -webkit-user-select: none; -ms-user-select: none; }
        .audio-feature .tooltip-text { visibility: hidden; white-space: nowrap; background-color: #111827; color: #fff; text-align: center; border-radius: 6px; padding: 8px 12px; position: absolute; z-index: 9999; bottom: 150%; left: 50%; transform: translateX(-50%); opacity: 0; transition: opacity 0.3s; font-size: 0.75rem; pointer-events: none; box-shadow: 0 4px 12px rgba(0,0,0,0.6); border: 1px solid #4b5563; }
        .audio-feature:hover .tooltip-text { visibility: visible; opacity: 1; }
        @media (max-width: 768px) { .audio-feature { cursor: pointer; } .tooltip-text.mobile-visible { visibility: visible !important; opacity: 1 !important; } }
        .stat-card { cursor: pointer; transition: all 0.3s ease; border: 1px solid #374151; background-color: rgba(31, 41, 55, 0.5); backdrop-filter: blur(4px); user-select: none; -webkit-user-select: none; -ms-user-select: none; }
        .stat-card:hover { border-color: #4f46e5; box-shadow: 0 0 15px rgba(79, 70, 229, 0.3); }
        .stat-card.active-filter { border-color: #818cf8; box-shadow: 0 0 20px rgba(129, 140, 248, 0.4); }
        .focus\:shadow-indigo:focus { box-shadow: 0 0 15px rgba(79, 70, 229, 0.3); }
        .loading-spinner { border: 4px solid #374151; border-top: 4px solid #6366f1; border-radius: 50%; width: 40px; height: 40px; animation: spin 1s linear infinite; }
        @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
        @media (min-width: 768px) { #cinema-list-header { display: grid; grid-template-columns: 3fr 2fr 1fr 1fr 1fr 1fr; gap: 1rem; padding: 1rem 1.5rem; background-color: transparent; font-weight: 600; color: #9ca3af; border-bottom: 2px solid #374151; } .cinema-card { display: grid; grid-template-columns: 3fr 2fr 1fr 1fr 1fr 1fr; gap: 1rem; align-items: center; padding: 1.25rem 1.5rem; margin-bottom: 0; border-radius: 0; border: none; border-bottom: 1px solid #374151; box-shadow: none; background-color: transparent; backdrop-filter: none; border-left: 3px solid transparent; } .cinema-card::before { display: none; } .cinema-card:hover { background-color: #37415130; transform: none; box-shadow: none; border-left-color: #6366f1; } .cinema-card:last-child { border-bottom: none; } .card-row { display: contents; } .card-label { display: none; } .card-value { color: #d1d5db; } .cinema-name { margin-bottom: 0; } .sortable { cursor: pointer; transition: color 0.2s ease; display: flex; align-items: center; user-select: none; -webkit-user-select: none; -ms-user-select: none; } .sortable:hover, .sortable.active { color: #f9fafb; } .sort-icon-container { margin-left: 0.5rem; display: flex; align-items: center; height: 16px; width: 16px; } .sort-icon { transition: all 0.3s ease; color: #6366f1; opacity: 0; transform: translateY(3px); } .sortable.active .sort-icon-asc:not(.hidden), .sortable.active .sort-icon-desc:not(.hidden) { opacity: 1; transform: translateY(0); } }
    </style>
</head>
<body class="text-gray-300 min-h-screen">
    <header class="bg-transparent text-white text-center py-12 md:py-16">
        <div class="max-w-4xl mx-auto px-4">
            <h1 id="pageTitleText" class="header-title text-4xl md:text-5xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-indigo-400 to-purple-400">
                激光<span class="imax-text">IMAX</span>影院分布
            </h1>
            <p class="text-lg opacity-80 mb-6">寻找当地最佳影院，探索极致观影体验</p>
            <div class="mode-switcher">
                <button id="btnLaser" class="mode-btn active">激光 IMAX</button>
                <button id="btnDigital" class="mode-btn">数字 IMAX</button>
            </div>
        </div>
    </header>
    <main class="max-w-6xl mx-auto px-4 py-8">
        <div class="mb-8 relative">
            <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                <svg class="h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z" clip-rule="evenodd" />
                </svg>
            </div>
            <input type="text" id="searchInput" name="cinema-search" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false" class="w-full py-3 pl-12 pr-12 text-white bg-gray-800 border border-gray-700 rounded-lg placeholder-gray-500 focus:outline-none focus:border-indigo-500 focus:shadow-indigo transition-all duration-300" placeholder="输入城市名，例：上海">
            <button id="clearSearchBtn" class="absolute inset-y-0 right-0 pr-4 flex items-center text-gray-500 hover:text-gray-300 transition-colors duration-200 hidden" type="button">
                <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M6.28 5.22a.75.75 0 00-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 101.06 1.06L10 11.06l3.72 3.72a.75.75 0 101.06-1.06L11.06 10l3.72-3.72a.75.75 0 00-1.06-1.06L10 8.94 6.28 5.22z" />
                </svg>
            </button>
        </div>
        <div id="statsSection" class="my-6 grid grid-cols-2 md:grid-cols-4 gap-4"></div>
        
        <!-- Mobile Sort Controls -->
        <div id="mobileSortContainer" class="md:hidden flex items-center justify-end mb-4 px-1">
            <label for="mobileSortSelect" class="text-sm text-gray-400 mr-2">排序:</label>
            <select id="mobileSortSelect" class="bg-gray-800 border border-gray-700 rounded-md py-1 px-2 text-white text-sm focus:outline-none focus:border-indigo-500">
                <!-- Options will be populated by JS -->
            </select>
        </div>

        <div class="md:bg-gray-800/50 md:rounded-lg md:border md:border-gray-700">
            <div id="cinema-list-header" class="hidden md:grid">
                <span class="sortable" data-sort="name">影城名称<span class="sort-icon-container"><svg class="sort-icon sort-icon-asc hidden" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16"><path fill-rule="evenodd" d="M8 15a.5.5 0 0 0 .5-.5V2.707l3.146 3.147a.5.5 0 0 0 .708-.708l-4-4a.5.5 0 0 0-.708 0l-4 4a.5.5 0 1 0 .708.708L7.5 2.707V14.5a.5.5 0 0 0 .5.5"/></svg><svg class="sort-icon sort-icon-desc hidden" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16"><path fill-rule="evenodd" d="M8 1a.5.5 0 0 1 .5.5v11.793l3.146-3.147a.5.5 0 0 1 .708.708l-4 4a.5.5 0 0 1-.708 0l-4-4a.5.5 0 0 1 .708-.708L7.5 13.293V1.5A.5.5 0 0 1 8 1"/></svg></span></span>
                <span class="sortable" data-sort="projectorType">放映机型号<span class="sort-icon-container"></span></span>
                <span class="sortable" data-sort="screenWidth">宽(m)<span class="sort-icon-container"></span></span>
                <span class="sortable" data-sort="screenHeight">高(m)<span class="sort-icon-container"></span></span>
                <span class="sortable" data-sort="screenArea">面积(㎡)<span class="sort-icon-container"></span></span>
                <span class="sortable" data-sort="seatCount">座位数<span class="sort-icon-container"></span></span>
            </div>
            <div id="cinema-list"></div>
            <p id="no-results" class="text-center py-16 text-gray-500 hidden">未找到匹配的影院</p>
        </div>
    </main>
    <footer class="text-white py-8 mt-4">
        <div class="max-w-6xl mx-auto px-4 text-center">
            <p class="text-gray-400 text-sm">
                数据来源：<a href="https://docs.qq.com/sheet/DQ3FEUUZJdklNSWJP?tab=BB08J2" target="_blank" rel="noopener noreferrer" class="text-indigo-400 hover:text-indigo-300 underline decoration-dotted underline-offset-4">@ArvinTingcn</a>
            </p>
        </div>
    </footer>
    <script>
// ====== DEMO 数据（激光/数字各3条） =====
const demoData = {
  laser: [
    { name: "上海CGV影城（虹口龙之梦IMAX）", projectorType: "Commercial Laser", screenWidth: 25.2, screenHeight: 13.6, screenArea: 342.72, seatCount: 500, audioSystem: "12声道" },
    { name: "北京UME华星IMAX", projectorType: "GT Laser", screenWidth: 28.0, screenHeight: 15.0, screenArea: 420.0, seatCount: 600, audioSystem: "12声道" },
    { name: "广州百丽宫IMAX", projectorType: "Laser XT", screenWidth: 22.0, screenHeight: 12.0, screenArea: 264.0, seatCount: 400, audioSystem: "7.1声道" }
  ],
  digital: [
    { name: "深圳百老汇影城IMAX", projectorType: "IMAX Xenon", screenWidth: 20.0, screenHeight: 10.5, screenArea: 210.0, seatCount: 350, audioSystem: "7.1声道" },
    { name: "成都万达IMAX", projectorType: "IMAX Xenon", screenWidth: 18.5, screenHeight: 9.8, screenArea: 181.3, seatCount: 320, audioSystem: "7.1声道" },
    { name: "杭州耀莱IMAX", projectorType: "IMAX Xenon", screenWidth: 19.2, screenHeight: 10.0, screenArea: 192.0, seatCount: 330, audioSystem: "7.1声道" }
  ]
};
// ====== IMAX Demo JS 逻辑 =====
let currentMode = 'laser';
let currentSort = { field: null, direction: null };
let activeFilter = 'all';
const listContainer = document.getElementById('cinema-list');
const searchInput = document.getElementById('searchInput');
const clearSearchBtn = document.getElementById('clearSearchBtn');
const noResults = document.getElementById('no-results');
const statsContainer = document.getElementById('statsSection');
const pageTitleText = document.getElementById('pageTitleText');
const btnLaser = document.getElementById('btnLaser');
const btnDigital = document.getElementById('btnDigital');
const mobileSortSelect = document.getElementById('mobileSortSelect');

function formatNumber(value) {
    if (!value || value === '--' || value === '') return '--';
    const num = parseFloat(value);
    return isNaN(num) ? '--' : num.toFixed(2);
}

function renderList(data) {
    listContainer.innerHTML = '';
    if (data.length === 0) { noResults.classList.remove('hidden'); return; }
    noResults.classList.add('hidden');
    data.forEach((cinema, index) => {
        const type = cinema.projectorType || '';
        let badgeStyle = 'bg-gray-700 text-gray-300 border-gray-600';
        if (currentMode === 'laser') {
            if (type.includes('Commercial')) badgeStyle = 'bg-blue-900/50 text-blue-300 border-blue-500/30';
            else if (type.includes('Laser XT')) badgeStyle = 'bg-green-900/50 text-green-300 border-green-500/30';
            else if (type.includes('GT')) badgeStyle = 'bg-purple-900/50 text-purple-300 border-purple-500/30';
        }
        const audioFeatureHTML = (cinema.audioSystem && cinema.audioSystem.includes('12声道'))
            ? `<div class="audio-feature" data-audio-id="audio-${index}"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="text-gray-400 hover:text-white transition" viewBox="0 0 16 16"><path d="M11.536 14.01A8.47 8.47 0 0 0 14.026 8a8.47 8.47 0 0 0-2.49-6.01l-.708.707A7.48 7.48 0 0 1 13.025 8c0 2.071-.84 3.946-2.197 5.303z"/><path d="M10.121 12.596A6.48 6.48 0 0 0 12.025 8a6.48 6.48 0 0 0-1.904-4.596l-.707.707A5.48 5.48 0 0 1 11.025 8a5.48 5.48 0 0 1-1.61 3.89z"/><path d="M8.707 11.182A4.5 4.5 0 0 0 10.025 8a4.5 4.5 0 0 0-1.318-3.182L8 5.525A3.5 3.5 0 0 1 9.025 8 3.5 3.5 0 0 1 8 10.475zM6.717 3.55A.5.5 0 0 1 7 4v8a.5.5 0 0 1-.812.39L3.825 10.5H1.5A.5.5 0 0 1 1 10V6a.5.5 0 0 1 .5-.5h2.325l2.363-1.89a.5.5 0 0 1 .529-.06z"/></svg><span class="tooltip-text">${cinema.audioSystem}</span></div>` : '';
        const cardHTML = `<div class="cinema-card" style="animation-delay: ${index * 50}ms;"><div class="cinema-name">${cinema.name}</div><div class="card-value flex items-center space-x-2"><span class="cinema-type-badge ${badgeStyle}">${cinema.projectorType}</span>${audioFeatureHTML}</div><div class="card-row md:contents"><span class="card-label">宽(m)</span><span class="card-value">${formatNumber(cinema.screenWidth)}</span></div><div class="card-row md:contents"><span class="card-label">高(m)</span><span class="card-value">${formatNumber(cinema.screenHeight)}</span></div><div class="card-row md:contents"><span class="card-label">面积(㎡)</span><span class="card-value">${formatNumber(cinema.screenArea)}</span></div><div class="card-row md:contents"><span class="card-label">座位数</span><span class="card-value">${cinema.seatCount || '--'}</span></div></div>`;
        listContainer.insertAdjacentHTML('beforeend', cardHTML);
    });
    bindAudioFeatureEvents();
}

function updateStats(data) {
    let statsHTML = '';
    if (currentMode === 'laser') {
        const commercial = data.filter(c => (c.projectorType || '').includes('Commercial')).length;
        const laserXT = data.filter(c => (c.projectorType || '').includes('Laser XT')).length;
        const gt3d = data.filter(c => (c.projectorType || '').includes('GT')).length;
        statsHTML = `
            <div class="stat-card bg-gray-800 p-4 rounded-xl border border-gray-700 text-center" data-filter="all"><div class="text-3xl font-bold text-indigo-400">${data.length}</div><div class="text-gray-400 mt-1 text-sm">总影院数</div></div>
            <div class="stat-card bg-gray-800 p-4 rounded-xl border border-gray-700 text-center" data-filter="GT"><div class="text-3xl font-bold text-purple-400">${gt3d}</div><div class="text-gray-400 mt-1 text-sm">一代 GT</div></div>
            <div class="stat-card bg-gray-800 p-4 rounded-xl border border-gray-700 text-center" data-filter="Commercial"><div class="text-3xl font-bold text-blue-400">${commercial}</div><div class="text-gray-400 mt-1 text-sm">二代 Cola</div></div>
            <div class="stat-card bg-gray-800 p-4 rounded-xl border border-gray-700 text-center" data-filter="Laser XT"><div class="text-3xl font-bold text-green-400">${laserXT}</div><div class="text-gray-400 mt-1 text-sm">三代 XT</div></div>
        `;
    } else {
        const count_gt_300 = data.filter(c => (parseFloat(c.screenArea) || 0) > 300).length;
        const count_200_300 = data.filter(c => { const area = parseFloat(c.screenArea) || 0; return area > 200 && area <= 300; }).length;
        const count_lt_200 = data.filter(c => (parseFloat(c.screenArea) || 0) <= 200 && (parseFloat(c.screenArea) || 0) > 0).length;
        statsHTML = `
            <div class="stat-card bg-gray-800 p-4 rounded-xl border border-gray-700 text-center" data-filter="all"><div class="text-3xl font-bold text-indigo-400">${data.length}</div><div class="text-gray-400 mt-1 text-sm">总影院数</div></div>
            <div class="stat-card bg-gray-800 p-4 rounded-xl border border-gray-700 text-center" data-filter="gt_300"><div class="text-3xl font-bold text-red-400">${count_gt_300}</div><div class="text-gray-400 mt-1 text-sm">大银幕(>300㎡)</div></div>
            <div class="stat-card bg-gray-800 p-4 rounded-xl border border-gray-700 text-center" data-filter="200_300"><div class="text-3xl font-bold text-orange-400">${count_200_300}</div><div class="text-gray-400 mt-1 text-sm">中银幕(200-300㎡)</div></div>
            <div class="stat-card bg-gray-800 p-4 rounded-xl border border-gray-700 text-center" data-filter="lt_200"><div class="text-3xl font-bold text-yellow-400">${count_lt_200}</div><div class="text-gray-400 mt-1 text-sm">小银幕(<200㎡)</div></div>
        `;
    }
    if (statsContainer) statsContainer.innerHTML = statsHTML;
    updateActiveFilterUI();
}
function updateActiveFilterUI() {
    document.querySelectorAll('.stat-card').forEach(card => {
        card.classList.toggle('active-filter', card.dataset.filter === activeFilter);
    });
}
function sortData(data, field, direction) {
    return [...data].sort((a, b) => {
        let aVal = a[field], bVal = b[field];
        if (["screenWidth", "screenHeight", "screenArea", "seatCount"].includes(field)) { aVal = parseFloat(aVal) || -1; bVal = parseFloat(bVal) || -1; }
        else { aVal = String(aVal || '').toLowerCase(); bVal = String(bVal || '').toLowerCase(); }
        if (direction === 'asc') return aVal > bVal ? 1 : -1;
        return aVal < bVal ? 1 : -1;
    });
}
function smartSort(data) {
    const typeOrder = { 'GT': 1, 'Commercial': 2, 'Laser XT': 3 };
    return [...data].sort((a, b) => {
        const aType = a.projectorType || '', bType = b.projectorType || '';
        let aOrder = 4, bOrder = 4;
        if (aType.includes('GT')) aOrder = typeOrder['GT'];
        else if (aType.includes('Commercial')) aOrder = typeOrder['Commercial'];
        else if (aType.includes('Laser XT')) aOrder = typeOrder['Laser XT'];
        if (bType.includes('GT')) bOrder = typeOrder['GT'];
        else if (bType.includes('Commercial')) bOrder = typeOrder['Commercial'];
        else if (bType.includes('Laser XT')) bOrder = typeOrder['Laser XT'];
        if (aOrder !== bOrder) return aOrder - bOrder;
        const aArea = parseFloat(a.screenArea) || 0, bArea = parseFloat(b.screenArea) || 0;
        return bArea - aArea;
    });
}
function updateSortIcons(field, direction) {
    document.querySelectorAll('.sortable').forEach(header => {
        const ascIcon = header.querySelector('.sort-icon-asc'), descIcon = header.querySelector('.sort-icon-desc');
        if (!ascIcon || !descIcon) return;
        header.classList.remove('active');
        ascIcon.classList.add('hidden'); descIcon.classList.add('hidden');
        if (header.dataset.sort === field && direction) {
            header.classList.add('active');
            if (direction === 'asc') ascIcon.classList.remove('hidden'); else descIcon.classList.remove('hidden');
        }
    });
}
function filterAndRender() {
    let baseData = [...demoData[currentMode]];
    const searchTerm = searchInput.value.toLowerCase();
    if (searchTerm.trim() !== '') {
        baseData = baseData.filter(c => (c.name || '').toLowerCase().includes(searchTerm));
    }
    updateStats(baseData);
    let dataToRender = [...baseData];
    if (activeFilter !== 'all') {
        if (currentMode === 'laser') {
            dataToRender = dataToRender.filter(c => (c.projectorType || '').includes(activeFilter));
        } else {
            dataToRender = dataToRender.filter(c => {
                const area = parseFloat(c.screenArea) || 0;
                switch (activeFilter) {
                    case 'gt_300': return area > 300;
                    case '200_300': return area > 200 && area <= 300;
                    case 'lt_200': return area <= 200 && area > 0;
                    default: return true;
                }
            });
        }
    }

    // Sorting logic
    if (currentSort.field && currentSort.direction) {
        dataToRender = sortData(dataToRender, currentSort.field, currentSort.direction);
    } else { // Default sorting logic
        if (currentMode === 'laser') {
            dataToRender = smartSort(dataToRender);
        } else { // 'digital' mode default sort by area descending
            dataToRender = sortData(dataToRender, 'screenArea', 'desc');
        }
    }
    
    renderList(dataToRender);
    updateSortIcons(currentSort.field, currentSort.direction);
    bindAudioFeatureEvents();
}
function bindAudioFeatureEvents() {
    if (window.innerWidth <= 768) {
        document.querySelectorAll('.audio-feature').forEach(audioFeature => {
            audioFeature.addEventListener('click', function(e) {
                e.stopPropagation();
                const tooltip = this.querySelector('.tooltip-text');
                const isCurrentlyVisible = tooltip.classList.contains('mobile-visible');
                document.querySelectorAll('.tooltip-text.mobile-visible').forEach(t => { t.classList.remove('mobile-visible'); });
                if (!isCurrentlyVisible) { tooltip.classList.add('mobile-visible'); }
            });
        });
    }
}
function handleDocumentClick(e) {
    if (window.innerWidth <= 768) {
        if (!e.target.closest('.audio-feature')) {
            document.querySelectorAll('.tooltip-text.mobile-visible').forEach(tooltip => {
                tooltip.classList.remove('mobile-visible');
            });
        }
    }
}
function switchModeAndRender(newMode) {
    if (newMode === currentMode) return;
    currentMode = newMode;
    const titleText = (newMode === 'laser' ? '激光' : '数字') + '<span class="imax-text">IMAX</span>影院分布';
    if (pageTitleText) pageTitleText.innerHTML = titleText;
    if (btnLaser) btnLaser.classList.toggle('active', newMode === 'laser');
    if (btnDigital) btnDigital.classList.toggle('active', newMode === 'digital');
    resetState();
    updateMobileSortOptions();
    filterAndRender();
}
function resetState() {
    currentSort = { field: null, direction: null };
    activeFilter = 'all';
    if(mobileSortSelect) mobileSortSelect.selectedIndex = 0;
}
function updateMobileSortOptions() {
    if (!mobileSortSelect) return;
    mobileSortSelect.innerHTML = '';
    let options = [];
    if (currentMode === 'laser') {
        options = [
            { value: 'smart', text: '智能推荐' },
            { value: 'screenArea_desc', text: '面积 ↓' },
            { value: 'screenArea_asc', text: '面积 ↑' },
            { value: 'seatCount_desc', text: '座位数 ↓' },
            { value: 'seatCount_asc', text: '座位数 ↑' },
        ];
    } else { // digital
        options = [
            { value: 'screenArea_desc', text: '面积 ↓ (默认)' },
            { value: 'screenArea_asc', text: '面积 ↑' },
            { value: 'seatCount_desc', text: '座位数 ↓' },
            { value: 'seatCount_asc', text: '座位数 ↑' },
        ];
    }
    options.forEach(opt => {
        mobileSortSelect.add(new Option(opt.text, opt.value));
    });
}

document.addEventListener('DOMContentLoaded', () => {
    const sortIconsHTML = document.querySelector('.sort-icon-container')?.innerHTML;
    if (sortIconsHTML) {
        document.querySelectorAll('.sortable .sort-icon-container').forEach(c => {
            if (!c.innerHTML.trim()) c.innerHTML = sortIconsHTML;
        });
    }
    const title = document.querySelector('.header-title');
    if (title) {
        title.addEventListener('click', () => {
            if (!title.classList.contains('neon-active')) {
                title.classList.add('neon-active');
                setTimeout(() => { title.classList.remove('neon-active'); }, 2000);
            }
        });
    }
    if (btnLaser) btnLaser.addEventListener('click', () => switchModeAndRender('laser'));
    if (btnDigital) btnDigital.addEventListener('click', () => switchModeAndRender('digital'));
    
    updateMobileSortOptions();
    filterAndRender();

    searchInput.addEventListener('input', () => { updateClearButton(); filterAndRender(); });
    clearSearchBtn.addEventListener('click', clearSearch);
    document.addEventListener('click', handleDocumentClick);
    statsContainer.addEventListener('click', e => {
        const card = e.target.closest('.stat-card');
        if (!card) return;
        const filterValue = card.dataset.filter;
        activeFilter = activeFilter === filterValue ? 'all' : filterValue;
        filterAndRender();
    });
    document.querySelectorAll('.sortable').forEach(header => {
        header.addEventListener('click', () => {
            const field = header.dataset.sort;
            if (currentSort.field !== field) { currentSort.field = field; currentSort.direction = 'desc'; }
            else if (currentSort.direction === 'desc') { currentSort.direction = 'asc'; }
            else { // Was asc, now reset to default
                currentSort.field = null;
                currentSort.direction = null; 
            }
            filterAndRender();
        });
    });
    mobileSortSelect.addEventListener('change', () => {
        const value = mobileSortSelect.value;
        if (value === 'smart' || (currentMode === 'digital' && value === 'screenArea_desc')) {
            currentSort = { field: null, direction: null };
        } else {
            const [field, direction] = value.split('_');
            currentSort = { field, direction };
        }
        filterAndRender();
    });
    if (pageTitleText) pageTitleText.innerHTML = '激光<span class="imax-text">IMAX</span>影院分布';
});
function updateClearButton() {
    if (searchInput.value.trim()) {
        clearSearchBtn.classList.remove('hidden');
    } else {
        clearSearchBtn.classList.add('hidden');
    }
}
function clearSearch() {
    searchInput.value = '';
    updateClearButton();
    filterAndRender();
    searchInput.focus();
}
</script>
</body>
</html>