// 全局变量
let cinemaData = [];
let originalData = [];
let currentSort = { field: null, direction: null };
let activeFilter = 'all';
let currentMode = 'laser'; // 'laser' or 'digital'
const dataCache = { laser: null, digital: null }; // 缓存已加载的数据

// DOM 元素引用
const listContainer = document.getElementById('cinema-list');
const searchInput = document.getElementById('searchInput');
const clearSearchBtn = document.getElementById('clearSearchBtn');
const noResults = document.getElementById('no-results');
const statsContainer = document.getElementById('statsSection');
const pageTitle = document.getElementById('pageTitle');
const pageTitleText = document.getElementById('pageTitleText');
const btnLaser = document.getElementById('btnLaser');
const btnDigital = document.getElementById('btnDigital');

// 更新清空按钮显示状态
function updateClearButton() {
    if (searchInput.value.trim()) {
        clearSearchBtn.classList.remove('hidden');
    } else {
        clearSearchBtn.classList.add('hidden');
    }
}

// 清空搜索框
function clearSearch() {
    searchInput.value = '';
    updateClearButton();
    filterAndRender();
    searchInput.focus();
}

// 显示加载状态
function showLoading(message) {
    listContainer.innerHTML = `<div class="text-center py-10"><div class="loading-spinner mx-auto mb-4"></div><p class="text-gray-400">${message}</p></div>`;
    if (statsContainer) {
        statsContainer.innerHTML = ''; // 清空旧统计数据
    }
    noResults.classList.add('hidden');
}

// 加载影院数据 - 通过API接口（支持不同类型）
async function loadCinemaData(mode) {
    // 如果数据已在缓存中，直接返回
    if (dataCache[mode]) {
        console.log(`✅ 从缓存加载 ${mode} 模式数据`);
        return dataCache[mode];
    }

    try {
        const type = mode === 'laser' ? 'laser' : 'digital';
        const response = await fetch(`/Pages/IMAX/ImaxDataApi.ashx?action=getCinemas&type=${type}`);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        const data = await response.json();

        // 检查是否是错误响应
        if (data.error) {
            throw new Error(data.message || '服务器返回错误');
        }

        console.log(`✅ 成功加载 ${data.length} 条来自 ${type} 的数据`);
        dataCache[mode] = data; // 缓存数据
        return data;
    } catch (error) {
        console.error(`❌ 加载影院数据失败:`, error);
        console.error('错误详情:', error.message);
        listContainer.innerHTML = `<div class="text-center py-10"><p class="text-red-400">错误：无法加载影院数据。<br>请确保服务器正常运行。</p></div>`;
        return [];
    }
}

// 格式化数字为小数点后两位
function formatNumber(value) {
    if (!value || value === '--' || value === '') return '--';
    const num = parseFloat(value);
    return isNaN(num) ? '--' : num.toFixed(2);
}

function renderList(data) {
    listContainer.innerHTML = '';
    if (data.length === 0) { noResults.classList.remove('hidden'); return; }
    noResults.classList.add('hidden');

    data.forEach((cinema, index) => {
        const type = cinema.projectorType || '';
        let badgeStyle = 'bg-gray-700 text-gray-300 border-gray-600'; // 默认/数字IMAX

        // 只有激光IMAX才区分不同类型的颜色
        if (currentMode === 'laser') {
            if (type.includes('Commercial')) badgeStyle = 'bg-blue-900/50 text-blue-300 border-blue-500/30';
            else if (type.includes('Laser XT')) badgeStyle = 'bg-green-900/50 text-green-300 border-green-500/30';
            else if (type.includes('GT')) badgeStyle = 'bg-purple-900/50 text-purple-300 border-purple-500/30';
        }

        const audioFeatureHTML = (cinema.audioSystem && cinema.audioSystem.includes('12声道'))
            ? `<div class="audio-feature" data-audio-id="audio-${index}"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="text-gray-400 hover:text-white transition" viewBox="0 0 16 16"><path d="M11.536 14.01A8.47 8.47 0 0 0 14.026 8a8.47 8.47 0 0 0-2.49-6.01l-.708.707A7.48 7.48 0 0 1 13.025 8c0 2.071-.84 3.946-2.197 5.303z"/><path d="M10.121 12.596A6.48 6.48 0 0 0 12.025 8a6.48 6.48 0 0 0-1.904-4.596l-.707.707A5.48 5.48 0 0 1 11.025 8a5.48 5.48 0 0 1-1.61 3.89z"/><path d="M8.707 11.182A4.5 4.5 0 0 0 10.025 8a4.5 4.5 0 0 0-1.318-3.182L8 5.525A3.5 3.5 0 0 1 9.025 8 3.5 3.5 0 0 1 8 10.475zM6.717 3.55A.5.5 0 0 1 7 4v8a.5.5 0 0 1-.812.39L3.825 10.5H1.5A.5.5 0 0 1 1 10V6a.5.5 0 0 1 .5-.5h2.325l2.363-1.89a.5.5 0 0 1 .529-.06z"/></svg><span class="tooltip-text">${cinema.audioSystem}</span></div>` : '';

        const cardHTML = `<div class="cinema-card" style="animation-delay: ${index * 50}ms;"><div class="cinema-name">${cinema.name}</div><div class="card-value flex items-center space-x-2"><span class="cinema-type-badge ${badgeStyle}">${cinema.projectorType}</span>${audioFeatureHTML}</div><div class="card-row md:contents"><span class="card-label">宽(m)</span><span class="card-value">${formatNumber(cinema.screenWidth)}</span></div><div class="card-row md:contents"><span class="card-label">高(m)</span><span class="card-value">${formatNumber(cinema.screenHeight)}</span></div><div class="card-row md:contents"><span class="card-label">面积(㎡)</span><span class="card-value">${formatNumber(cinema.screenArea)}</span></div><div class="card-row md:contents"><span class="card-label">座位数</span><span class="card-value">${cinema.seatCount || '--'}</span></div></div>`;
        listContainer.insertAdjacentHTML('beforeend', cardHTML);
    });

    // 重新绑定音响图标点击事件（移动端）
    bindAudioFeatureEvents();
}

function updateStats(data) {
    let statsHTML = '';

    if (currentMode === 'laser') {
        // 激光IMAX按放映机类型统计
        const commercial = data.filter(c => (c.projectorType || '').includes('Commercial')).length;
        const laserXT = data.filter(c => (c.projectorType || '').includes('Laser XT')).length;
        const gt3d = data.filter(c => (c.projectorType || '').includes('GT')).length;

        statsHTML = `
            <div class="stat-card bg-gray-800 p-4 rounded-xl border border-gray-700 text-center" data-filter="all"><div class="text-3xl font-bold text-indigo-400">${data.length}</div><div class="text-gray-400 mt-1 text-sm">总影院数</div></div>
            <div class="stat-card bg-gray-800 p-4 rounded-xl border border-gray-700 text-center" data-filter="GT"><div class="text-3xl font-bold text-purple-400">${gt3d}</div><div class="text-gray-400 mt-1 text-sm">一代 GT</div></div>
            <div class="stat-card bg-gray-800 p-4 rounded-xl border border-gray-700 text-center" data-filter="Commercial"><div class="text-3xl font-bold text-blue-400">${commercial}</div><div class="text-gray-400 mt-1 text-sm">二代 Cola</div></div>
            <div class="stat-card bg-gray-800 p-4 rounded-xl border border-gray-700 text-center" data-filter="Laser XT"><div class="text-3xl font-bold text-green-400">${laserXT}</div><div class="text-gray-400 mt-1 text-sm">三代 XT</div></div>
        `;
    } else {
        // 数字IMAX按银幕面积统计
        const count_gt_300 = data.filter(c => (parseFloat(c.screenArea) || 0) > 300).length;
        const count_200_300 = data.filter(c => {
            const area = parseFloat(c.screenArea) || 0;
            return area > 200 && area <= 300;
        }).length;
        const count_lt_200 = data.filter(c => (parseFloat(c.screenArea) || 0) <= 200 && (parseFloat(c.screenArea) || 0) > 0).length;

        statsHTML = `
            <div class="stat-card bg-gray-800 p-4 rounded-xl border border-gray-700 text-center" data-filter="all"><div class="text-3xl font-bold text-indigo-400">${data.length}</div><div class="text-gray-400 mt-1 text-sm">总影院数</div></div>
            <div class="stat-card bg-gray-800 p-4 rounded-xl border border-gray-700 text-center" data-filter="gt_300"><div class="text-3xl font-bold text-red-400">${count_gt_300}</div><div class="text-gray-400 mt-1 text-sm">大银幕 (>300㎡)</div></div>
            <div class="stat-card bg-gray-800 p-4 rounded-xl border border-gray-700 text-center" data-filter="200_300"><div class="text-3xl font-bold text-orange-400">${count_200_300}</div><div class="text-gray-400 mt-1 text-sm">中银幕 (200-300㎡)</div></div>
            <div class="stat-card bg-gray-800 p-4 rounded-xl border border-gray-700 text-center" data-filter="lt_200"><div class="text-3xl font-bold text-yellow-400">${count_lt_200}</div><div class="text-gray-400 mt-1 text-sm">小银幕 (<200㎡)</div></div>
        `;
    }

    if (statsContainer) {
        statsContainer.innerHTML = statsHTML;
    }
    updateActiveFilterUI();
}

function updateActiveFilterUI() {
    document.querySelectorAll('.stat-card').forEach(card => {
        card.classList.toggle('active-filter', card.dataset.filter === activeFilter);
    });
}

function sortData(data, field, direction) {
    return [...data].sort((a, b) => {
        let aVal = a[field], bVal = b[field];
        if (['screenWidth', 'screenHeight', 'screenArea', 'seatCount'].includes(field)) { aVal = parseFloat(aVal) || -1; bVal = parseFloat(bVal) || -1; }
        else { aVal = String(aVal || '').toLowerCase(); bVal = String(bVal || '').toLowerCase(); }
        if (direction === 'asc') return aVal > bVal ? 1 : -1;
        return aVal < bVal ? 1 : -1;
    });
}

function smartSort(data) {
    const typeOrder = {
        'GT': 1,
        'Commercial': 2,
        'Laser XT': 3
    };

    return [...data].sort((a, b) => {
        const aType = a.projectorType || '';
        const bType = b.projectorType || '';

        let aOrder = 4;
        let bOrder = 4;

        if (aType.includes('GT')) aOrder = typeOrder['GT'];
        else if (aType.includes('Commercial')) aOrder = typeOrder['Commercial'];
        else if (aType.includes('Laser XT')) aOrder = typeOrder['Laser XT'];

        if (bType.includes('GT')) bOrder = typeOrder['GT'];
        else if (bType.includes('Commercial')) bOrder = typeOrder['Commercial'];
        else if (bType.includes('Laser XT')) bOrder = typeOrder['Laser XT'];

        if (aOrder !== bOrder) {
            return aOrder - bOrder;
        }

        const aArea = parseFloat(a.screenArea) || 0;
        const bArea = parseFloat(b.screenArea) || 0;
        return bArea - aArea;
    });
}

function updateSortIcons(field, direction) {
    document.querySelectorAll('.sortable').forEach(header => {
        const ascIcon = header.querySelector('.sort-icon-asc'), descIcon = header.querySelector('.sort-icon-desc');
        if (!ascIcon || !descIcon) return;
        header.classList.remove('active');
        ascIcon.classList.add('hidden'); descIcon.classList.add('hidden');
        if (header.dataset.sort === field && direction) {
            header.classList.add('active');
            if (direction === 'asc') ascIcon.classList.remove('hidden'); else descIcon.classList.remove('hidden');
        }
    });
}

function filterAndRender() {
    let baseData = [...originalData];

    const searchTerm = searchInput.value.toLowerCase();
    const hasSearchTerm = searchTerm.trim() !== '';

    if (hasSearchTerm) {
        baseData = baseData.filter(c => (c.name || '').toLowerCase().includes(searchTerm));
    }

    updateStats(baseData);

    let dataToRender = [...baseData];
    if (activeFilter !== 'all') {
        if (currentMode === 'laser') {
            // 激光IMAX按放映机类型筛选
            dataToRender = dataToRender.filter(c => (c.projectorType || '').includes(activeFilter));
        } else {
            // 数字IMAX按银幕面积筛选
            dataToRender = dataToRender.filter(c => {
                const area = parseFloat(c.screenArea) || 0;
                switch (activeFilter) {
                    case 'gt_300': return area > 300;
                    case '200_300': return area > 200 && area <= 300;
                    case 'lt_200': return area <= 200 && area > 0;
                    default: return true;
                }
            });
        }
    }

    if (hasSearchTerm && !currentSort.direction) {
        dataToRender = smartSort(dataToRender);
    } else if (currentSort.direction) {
        dataToRender = sortData(dataToRender, currentSort.field, currentSort.direction);
    }

    renderList(dataToRender);
    updateSortIcons(currentSort.field, currentSort.direction);

    // 重新绑定音响图标点击事件（移动端）
    bindAudioFeatureEvents();
}

// 移动端音响图标点击事件处理
function bindAudioFeatureEvents() {
    // 只在移动端绑定点击事件
    if (window.innerWidth <= 768) {
        document.querySelectorAll('.audio-feature').forEach(audioFeature => {
            audioFeature.addEventListener('click', function(e) {
                e.stopPropagation(); // 防止事件冒泡

                const tooltip = this.querySelector('.tooltip-text');
                const isCurrentlyVisible = tooltip.classList.contains('mobile-visible');

                // 先隐藏所有其他的tooltip
                document.querySelectorAll('.tooltip-text.mobile-visible').forEach(t => {
                    t.classList.remove('mobile-visible');
                });

                // 切换当前tooltip的显示状态
                if (!isCurrentlyVisible) {
                    tooltip.classList.add('mobile-visible');
                }
            });
        });
    }
}

// 点击其他区域关闭tooltip
function handleDocumentClick(e) {
    if (window.innerWidth <= 768) {
        // 如果点击的不是音响图标区域，则隐藏所有tooltip
        if (!e.target.closest('.audio-feature')) {
            document.querySelectorAll('.tooltip-text.mobile-visible').forEach(tooltip => {
                tooltip.classList.remove('mobile-visible');
            });
        }
    }
}

// 模式切换主函数
async function switchModeAndRender(newMode) {
    if (newMode === currentMode && dataCache[newMode]) return; // 如果模式未变且数据已加载，则不执行

    currentMode = newMode;

    // 1. 更新UI状态
    const titleText = newMode === 'laser' ? '激光 IMAX 影院分布' : '数字 IMAX 影院分布';
    if (pageTitleText) {
        pageTitleText.textContent = titleText;
    }
    if (pageTitle) {
        pageTitle.textContent = titleText;
    }
    document.title = titleText;

    if (btnLaser) btnLaser.classList.toggle('active', newMode === 'laser');
    if (btnDigital) btnDigital.classList.toggle('active', newMode === 'digital');

    showLoading(`正在加载 ${newMode === 'laser' ? '激光' : '数字'} IMAX 数据...`);

    // 2. 重置状态
    resetState();

    // 3. 加载新数据
    originalData = await loadCinemaData(newMode);

    // 4. 初始渲染
    filterAndRender();
}

// 状态重置函数
function resetState() {
    if (searchInput) searchInput.value = '';
    updateClearButton();
    currentSort = { field: null, direction: null };
    activeFilter = 'all';
}

document.addEventListener('DOMContentLoaded', async () => {
    // 初始化排序图标
    const sortIconsHTML = document.querySelector('.sort-icon-container')?.innerHTML;
    if (sortIconsHTML) {
        document.querySelectorAll('.sortable .sort-icon-container').forEach(c => {
            if (!c.innerHTML.trim()) c.innerHTML = sortIconsHTML;
        });
    }

    // 标题点击彩蛋逻辑
    const title = document.querySelector('.header-title');
    if (title) {
        title.addEventListener('click', () => {
            if (!title.classList.contains('neon-active')) {
                title.classList.add('neon-active');
                setTimeout(() => {
                    title.classList.remove('neon-active');
                }, 2000); // 动画持续2秒
            }
        });
    }

    // 模式切换按钮事件
    if (btnLaser) {
        btnLaser.addEventListener('click', () => switchModeAndRender('laser'));
    }
    if (btnDigital) {
        btnDigital.addEventListener('click', () => switchModeAndRender('digital'));
    }

    // 首次加载，默认进入激光模式
    await switchModeAndRender('laser');

    // 搜索框事件监听
    searchInput.addEventListener('input', () => {
        updateClearButton();
        filterAndRender();
    });

    // 清空按钮事件监听
    clearSearchBtn.addEventListener('click', clearSearch);

    // 文档点击事件监听（用于关闭移动端tooltip）
    document.addEventListener('click', handleDocumentClick);
    
    document.getElementById('statsSection').addEventListener('click', e => {
        const card = e.target.closest('.stat-card');
        if (!card) return;
        const filterValue = card.dataset.filter;
        activeFilter = activeFilter === filterValue ? 'all' : filterValue;
        filterAndRender();
    });

    document.querySelectorAll('.sortable').forEach(header => {
        header.addEventListener('click', () => {
            const field = header.dataset.sort;
            if (currentSort.field !== field) {
                // 点击新字段，从降序开始
                currentSort.field = field;
                currentSort.direction = 'desc';
            } else if (currentSort.direction === 'desc') {
                // 当前是降序，切换到升序
                currentSort.direction = 'asc';
            } else if (currentSort.direction === 'asc') {
                // 当前是升序，切换到重置
                currentSort.direction = null;
            } else {
                // 当前是重置状态，重新开始循环（降序）
                currentSort.direction = 'desc';
            }
            filterAndRender();
        });
    });
});