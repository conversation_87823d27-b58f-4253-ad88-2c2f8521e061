// 全局变量
let cinemaData = [];
let originalData = [];
let currentSort = { field: null, direction: null };

const listContainer = document.getElementById('cinema-list');
const searchInput = document.getElementById('searchInput');
const clearSearchBtn = document.getElementById('clearSearchBtn');
const noResults = document.getElementById('no-results');

let activeFilter = 'all';

// 更新清空按钮显示状态
function updateClearButton() {
    if (searchInput.value.trim()) {
        clearSearchBtn.classList.remove('hidden');
    } else {
        clearSearchBtn.classList.add('hidden');
    }
}

// 清空搜索框
function clearSearch() {
    searchInput.value = '';
    updateClearButton();
    filterAndRender();
    searchInput.focus();
}

// 加载影院数据 - 通过API接口
async function loadCinemaData() {
    try {
        const response = await fetch('/Pages/IMAX/ImaxDataApi.ashx?action=getCinemas');
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        const data = await response.json();

        // 检查是否是错误响应
        if (data.error) {
            throw new Error(data.message || '服务器返回错误');
        }

        console.log('✅ API返回数据:', data.length, '家影院');
        return data;
    } catch (error) {
        console.error('❌ 加载影院数据失败:', error);
        console.error('错误详情:', error.message);
        return [];
    }
}

// 格式化数字为小数点后两位
function formatNumber(value) {
    if (!value || value === '--' || value === '') return '--';
    const num = parseFloat(value);
    return isNaN(num) ? '--' : num.toFixed(2);
}

function renderList(data) {
    listContainer.innerHTML = '';
    if (data.length === 0) { noResults.classList.remove('hidden'); return; }
    noResults.classList.add('hidden');

    data.forEach((cinema, index) => {
        const type = cinema.projectorType || '';
        let badgeStyle = 'bg-gray-700 text-gray-300 border-gray-600';
        if (type.includes('Commercial')) badgeStyle = 'bg-blue-900/50 text-blue-300 border-blue-500/30';
        else if (type.includes('Laser XT')) badgeStyle = 'bg-green-900/50 text-green-300 border-green-500/30';
        else if (type.includes('GT')) badgeStyle = 'bg-purple-900/50 text-purple-300 border-purple-500/30';

        const audioFeatureHTML = (cinema.audioSystem && cinema.audioSystem.includes('12声道'))
            ? `<div class="audio-feature" data-audio-id="audio-${index}"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="text-gray-400 hover:text-white transition" viewBox="0 0 16 16"><path d="M11.536 14.01A8.47 8.47 0 0 0 14.026 8a8.47 8.47 0 0 0-2.49-6.01l-.708.707A7.48 7.48 0 0 1 13.025 8c0 2.071-.84 3.946-2.197 5.303z"/><path d="M10.121 12.596A6.48 6.48 0 0 0 12.025 8a6.48 6.48 0 0 0-1.904-4.596l-.707.707A5.48 5.48 0 0 1 11.025 8a5.48 5.48 0 0 1-1.61 3.89z"/><path d="M8.707 11.182A4.5 4.5 0 0 0 10.025 8a4.5 4.5 0 0 0-1.318-3.182L8 5.525A3.5 3.5 0 0 1 9.025 8 3.5 3.5 0 0 1 8 10.475zM6.717 3.55A.5.5 0 0 1 7 4v8a.5.5 0 0 1-.812.39L3.825 10.5H1.5A.5.5 0 0 1 1 10V6a.5.5 0 0 1 .5-.5h2.325l2.363-1.89a.5.5 0 0 1 .529-.06z"/></svg><span class="tooltip-text">${cinema.audioSystem}</span></div>` : '';

        const cardHTML = `<div class="cinema-card"><div class="cinema-name">${cinema.name}</div><div class="card-value flex items-center space-x-2"><span class="cinema-type-badge ${badgeStyle}">${cinema.projectorType}</span>${audioFeatureHTML}</div><div class="card-row md:contents"><span class="card-label">宽(m)</span><span class="card-value">${formatNumber(cinema.screenWidth)}</span></div><div class="card-row md:contents"><span class="card-label">高(m)</span><span class="card-value">${formatNumber(cinema.screenHeight)}</span></div><div class="card-row md:contents"><span class="card-label">面积(㎡)</span><span class="card-value">${formatNumber(cinema.screenArea)}</span></div><div class="card-row md:contents"><span class="card-label">座位数</span><span class="card-value">${cinema.seatCount || '--'}</span></div></div>`;
        listContainer.insertAdjacentHTML('beforeend', cardHTML);
    });
}

function updateStats(data) {
    const statsContainer = document.getElementById('statsSection');
    const commercial = data.filter(c => (c.projectorType || '').includes('Commercial')).length;
    const laserXT = data.filter(c => (c.projectorType || '').includes('Laser XT')).length;
    const gt3d = data.filter(c => (c.projectorType || '').includes('GT')).length;
    
    statsContainer.innerHTML = `
        <div class="stat-card bg-gray-800 p-4 rounded-xl border border-gray-700 text-center" data-filter="all"><div class="text-3xl font-bold text-indigo-400">${data.length}</div><div class="text-gray-400 mt-1 text-sm">总影院数</div></div>
        <div class="stat-card bg-gray-800 p-4 rounded-xl border border-gray-700 text-center" data-filter="GT"><div class="text-3xl font-bold text-purple-400">${gt3d}</div><div class="text-gray-400 mt-1 text-sm">一代 GT</div></div>
        <div class="stat-card bg-gray-800 p-4 rounded-xl border border-gray-700 text-center" data-filter="Commercial"><div class="text-3xl font-bold text-blue-400">${commercial}</div><div class="text-gray-400 mt-1 text-sm">二代 Cola</div></div>
        <div class="stat-card bg-gray-800 p-4 rounded-xl border border-gray-700 text-center" data-filter="Laser XT"><div class="text-3xl font-bold text-green-400">${laserXT}</div><div class="text-gray-400 mt-1 text-sm">三代 XT</div></div>
    `;
    updateActiveFilterUI();
}

function updateActiveFilterUI() {
    document.querySelectorAll('.stat-card').forEach(card => {
        card.classList.toggle('active-filter', card.dataset.filter === activeFilter);
    });
}

function sortData(data, field, direction) {
    return [...data].sort((a, b) => {
        let aVal = a[field], bVal = b[field];
        if (['screenWidth', 'screenHeight', 'screenArea', 'seatCount'].includes(field)) { aVal = parseFloat(aVal) || -1; bVal = parseFloat(bVal) || -1; }
        else { aVal = String(aVal || '').toLowerCase(); bVal = String(bVal || '').toLowerCase(); }
        if (direction === 'asc') return aVal > bVal ? 1 : -1;
        return aVal < bVal ? 1 : -1;
    });
}

function smartSort(data) {
    const typeOrder = {
        'GT': 1,
        'Commercial': 2,
        'Laser XT': 3
    };

    return [...data].sort((a, b) => {
        const aType = a.projectorType || '';
        const bType = b.projectorType || '';

        let aOrder = 4;
        let bOrder = 4;

        if (aType.includes('GT')) aOrder = typeOrder['GT'];
        else if (aType.includes('Commercial')) aOrder = typeOrder['Commercial'];
        else if (aType.includes('Laser XT')) aOrder = typeOrder['Laser XT'];

        if (bType.includes('GT')) bOrder = typeOrder['GT'];
        else if (bType.includes('Commercial')) bOrder = typeOrder['Commercial'];
        else if (bType.includes('Laser XT')) bOrder = typeOrder['Laser XT'];

        if (aOrder !== bOrder) {
            return aOrder - bOrder;
        }

        const aArea = parseFloat(a.screenArea) || 0;
        const bArea = parseFloat(b.screenArea) || 0;
        return bArea - aArea;
    });
}

function updateSortIcons(field, direction) {
    document.querySelectorAll('.sortable').forEach(header => {
        const ascIcon = header.querySelector('.sort-icon-asc'), descIcon = header.querySelector('.sort-icon-desc');
        if (!ascIcon || !descIcon) return;
        header.classList.remove('active');
        ascIcon.classList.add('hidden'); descIcon.classList.add('hidden');
        if (header.dataset.sort === field && direction) {
            header.classList.add('active');
            if (direction === 'asc') ascIcon.classList.remove('hidden'); else descIcon.classList.remove('hidden');
        }
    });
}

function filterAndRender() {
    let baseData = [...originalData];

    const searchTerm = searchInput.value.toLowerCase();
    const hasSearchTerm = searchTerm.trim() !== '';

    if (hasSearchTerm) {
        baseData = baseData.filter(c => (c.name || '').toLowerCase().includes(searchTerm));
    }

    updateStats(baseData);

    let dataToRender = [...baseData];
    if (activeFilter !== 'all') {
        dataToRender = dataToRender.filter(c => (c.projectorType || '').includes(activeFilter));
    }

    if (hasSearchTerm && !currentSort.direction) {
        dataToRender = smartSort(dataToRender);
    } else if (currentSort.direction) {
        dataToRender = sortData(dataToRender, currentSort.field, currentSort.direction);
    }

    renderList(dataToRender);
    updateSortIcons(currentSort.field, currentSort.direction);

    // 重新绑定音响图标点击事件（移动端）
    bindAudioFeatureEvents();
}

// 移动端音响图标点击事件处理
function bindAudioFeatureEvents() {
    // 只在移动端绑定点击事件
    if (window.innerWidth <= 768) {
        document.querySelectorAll('.audio-feature').forEach(audioFeature => {
            audioFeature.addEventListener('click', function(e) {
                e.stopPropagation(); // 防止事件冒泡

                const tooltip = this.querySelector('.tooltip-text');
                const isCurrentlyVisible = tooltip.classList.contains('mobile-visible');

                // 先隐藏所有其他的tooltip
                document.querySelectorAll('.tooltip-text.mobile-visible').forEach(t => {
                    t.classList.remove('mobile-visible');
                });

                // 切换当前tooltip的显示状态
                if (!isCurrentlyVisible) {
                    tooltip.classList.add('mobile-visible');
                }
            });
        });
    }
}

// 点击其他区域关闭tooltip
function handleDocumentClick(e) {
    if (window.innerWidth <= 768) {
        // 如果点击的不是音响图标区域，则隐藏所有tooltip
        if (!e.target.closest('.audio-feature')) {
            document.querySelectorAll('.tooltip-text.mobile-visible').forEach(tooltip => {
                tooltip.classList.remove('mobile-visible');
            });
        }
    }
}

document.addEventListener('DOMContentLoaded', async () => {
    // 显示加载状态
    listContainer.innerHTML = '<div class="text-center py-10"><div class="loading-spinner mx-auto mb-4"></div><p class="text-gray-400">正在加载影院数据...</p></div>';

    // 加载数据
    cinemaData = await loadCinemaData();
    originalData = [...cinemaData];

    // 如果没有数据，显示提示信息
    if (cinemaData.length === 0) {
        listContainer.innerHTML = '<div class="text-center py-16"><div class="text-yellow-400 text-lg mb-4">⚠️ 暂无影院数据</div><p class="text-gray-500">请稍后重试或联系管理员。</p></div>';
        return;
    }

    // 初始化排序图标
    const sortIconsHTML = document.querySelector('.sort-icon-container').innerHTML;
    document.querySelectorAll('.sortable .sort-icon-container').forEach(c => { if (!c.innerHTML.trim()) c.innerHTML = sortIconsHTML; });

    // 标题点击彩蛋逻辑
    const title = document.querySelector('.header-title');
    title.addEventListener('click', () => {
        if (!title.classList.contains('neon-active')) {
            title.classList.add('neon-active');
            setTimeout(() => {
                title.classList.remove('neon-active');
            }, 2000); // 动画持续2秒
        }
    });

    // 首次渲染
    filterAndRender();

    // 搜索框事件监听
    searchInput.addEventListener('input', () => {
        updateClearButton();
        filterAndRender();
    });

    // 清空按钮事件监听
    clearSearchBtn.addEventListener('click', clearSearch);

    // 文档点击事件监听（用于关闭移动端tooltip）
    document.addEventListener('click', handleDocumentClick);
    
    document.getElementById('statsSection').addEventListener('click', e => {
        const card = e.target.closest('.stat-card');
        if (!card) return;
        const filterValue = card.dataset.filter;
        activeFilter = activeFilter === filterValue ? 'all' : filterValue;
        filterAndRender();
    });

    document.querySelectorAll('.sortable').forEach(header => {
        header.addEventListener('click', () => {
            const field = header.dataset.sort;
            if (currentSort.field !== field) { currentSort.field = field; currentSort.direction = 'desc'; } 
            else if (currentSort.direction === 'desc') { currentSort.direction = 'asc'; } 
            else { currentSort.direction = null; }
            filterAndRender();
        });
    });
});