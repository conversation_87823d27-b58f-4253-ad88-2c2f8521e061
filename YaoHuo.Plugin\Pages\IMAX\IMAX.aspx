<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="IMAX.aspx.cs" Inherits="YaoHuo.Plugin.Pages.IMAX.IMAX" %><!DOCTYPE html>
<html lang="zh-CN">
<head runat="server">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title id="pageTitle">激光 IMAX 影院分布</title>
    <meta name="description" content="寻找国内最佳 IMAX 影院，探索极致观影体验">
    <meta name="keywords" content="IMAX影院,IMAX影院列表,IMAX中国,IMAX中国影院,IMAX特效厅,激光IMAX,数字IMAX,IMAX影院查询,IMAX Laser,IMAX GT,IMAX Cola,IMAX XT,GT 3D,Commercial Laser,Laser XT,IMAX氙灯"/>
    <link rel="stylesheet" href="/Pages/IMAX/tailwind.min.css?v0.8">
    <link rel="stylesheet" href="/Pages/IMAX/IMAX.css?v0.9">
</head>
<body class="text-gray-300 min-h-screen">
    <header class="bg-transparent text-white text-center py-12 md:py-16">
        <div class="max-w-4xl mx-auto px-4">
            <h1 id="pageTitleText" class="header-title text-4xl md:text-5xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-indigo-400 to-purple-400">
                激光<span class="imax-text">IMAX</span>影院分布
            </h1>
            <p class="text-lg opacity-80 mb-6">寻找当地最佳影院，探索极致观影体验</p>

            <!-- 模式切换器 -->
            <div class="mode-switcher">
                <button id="btnLaser" class="mode-btn active">激光 IMAX</button>
                <button id="btnDigital" class="mode-btn">数字 IMAX</button>
            </div>
        </div>
    </header>
    <main class="max-w-6xl mx-auto px-4 py-8">
        <div class="mb-8 relative">
            <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                <svg class="h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z" clip-rule="evenodd" />
                </svg>
            </div>
            <input
                type="text"
                id="searchInput"
                name="cinema-search"
                autocomplete="off"
                autocorrect="off"
                autocapitalize="off"
                spellcheck="false"
                class="w-full py-3 pl-12 pr-12 text-white bg-gray-800 border border-gray-700 rounded-lg placeholder-gray-500 focus:outline-none focus:border-indigo-500 focus:shadow-indigo transition-all duration-300"
                placeholder="输入城市名，例：上海"
            >
            <button
                id="clearSearchBtn"
                class="absolute inset-y-0 right-0 pr-4 flex items-center text-gray-500 hover:text-gray-300 transition-colors duration-200 hidden"
                type="button"
            >
                <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M6.28 5.22a.75.75 0 00-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 101.06 1.06L10 11.06l3.72 3.72a.75.75 0 101.06-1.06L11.06 10l3.72-3.72a.75.75 0 00-1.06-1.06L10 8.94 6.28 5.22z" />
                </svg>
            </button>
        </div>
        <div id="statsSection" class="my-6 grid grid-cols-2 md:grid-cols-4 gap-4">
        </div>
        
        <!-- Mobile Sort Controls -->
        <div id="mobileSortContainer" class="md:hidden flex items-center justify-end mb-4 px-1">
            <label for="mobileSortSelect" class="text-sm text-gray-400 mr-2">排序:</label>
            <select id="mobileSortSelect" class="bg-gray-800 border border-gray-700 rounded-md py-1 px-2 text-white text-sm focus:outline-none focus:border-indigo-500">
                <!-- Options will be populated by JS -->
            </select>
        </div>
        <div class="md:bg-gray-800/50 md:rounded-lg md:border md:border-gray-700">
            <div id="cinema-list-header" class="hidden md:grid">
                <span class="sortable" data-sort="name">影城名称<span class="sort-icon-container"><svg class="sort-icon sort-icon-asc hidden" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16"><path fill-rule="evenodd" d="M8 15a.5.5 0 0 0 .5-.5V2.707l3.146 3.147a.5.5 0 0 0 .708-.708l-4-4a.5.5 0 0 0-.708 0l-4 4a.5.5 0 1 0 .708.708L7.5 2.707V14.5a.5.5 0 0 0 .5.5"/></svg><svg class="sort-icon sort-icon-desc hidden" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16"><path fill-rule="evenodd" d="M8 1a.5.5 0 0 1 .5.5v11.793l3.146-3.147a.5.5 0 0 1 .708.708l-4 4a.5.5 0 0 1-.708 0l-4-4a.5.5 0 0 1 .708-.708L7.5 13.293V1.5A.5.5 0 0 1 8 1"/></svg></span></span>
                <span class="sortable" data-sort="projectorType">放映机型号<span class="sort-icon-container"></span></span>
                <span class="sortable" data-sort="screenWidth">宽(m)<span class="sort-icon-container"></span></span>
                <span class="sortable" data-sort="screenHeight">高(m)<span class="sort-icon-container"></span></span>
                <span class="sortable" data-sort="screenArea">面积(㎡)<span class="sort-icon-container"></span></span>
                <span class="sortable" data-sort="seatCount">座位数<span class="sort-icon-container"></span></span>
            </div>
            <div id="cinema-list"></div>
            <p id="no-results" class="text-center py-16 text-gray-500 hidden">未找到匹配的影院</p>
        </div>
    </main>
    <footer class="text-white py-8 mt-4">
        <div class="max-w-6xl mx-auto px-4 text-center">
            <p class="text-gray-400 text-sm">
                数据来源：<a href="https://docs.qq.com/sheet/DQ3FEUUZJdklNSWJP?tab=BB08J2" target="_blank" rel="noopener noreferrer" class="text-indigo-400 hover:text-indigo-300 underline decoration-dotted underline-offset-4">@ArvinTingcn</a>
            </p>
        </div>
    </footer>
    <script src="/Pages/IMAX/IMAX.js?v0.8"></script>
</body>
</html>