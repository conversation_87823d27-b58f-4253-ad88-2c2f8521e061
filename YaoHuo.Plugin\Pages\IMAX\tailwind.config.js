/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./IMAX.aspx",
    "./IMAX.js",
    "./IMAX.css",
    "./tailwind.input.css"
  ],
  safelist: [
    // 确保这些动态生成的类被包含
    'text-center',
    'py-10',
    'text-red-400',
    'space-x-2',
    'bg-gray-700',
    'text-gray-300',
    'border-gray-600',
    'bg-gray-900/50',
    'text-green-300',
    'border-green-500/30',
    'bg-purple-900/50',
    'text-purple-300',
    'border-purple-500/30',
    'text-3xl',
    'font-bold',
    'text-indigo-400',
    'text-purple-400',
    'text-blue-400',
    'text-green-400',
    'text-red-400',
    'text-orange-400',
    'text-yellow-400',
    'p-4',
    'rounded-xl',
    'hover:text-white',
    'transition',
    'active',
    'bg-green-600',
    'text-white',
    'bg-blue-600',
    'bg-purple-600',
    'bg-gray-600',
    'loading-spinner',
    'mx-auto',
    'mb-4',
    'text-gray-400',
    'active-filter',
    'neon-active',
    'mobile-visible'
  ],
  theme: {
    extend: {
      colors: {
        primary: '#58b4b0',
      },
      fontFamily: {
        'orbitron': ['Orbitron', 'sans-serif'],
      },
    },
  },
  plugins: [],
}
