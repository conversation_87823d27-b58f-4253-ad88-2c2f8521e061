body {
    background-color: #111827;
    background-image: radial-gradient(ellipse at top, #1f2937, #111827 70%);
    font-family: 'Roboto', sans-serif;
    overflow-x: hidden;
}

/* 使用更高优先级的选择器，避免使用 !important */
header .header-title.header-title {
    font-family: 'Orbitron', sans-serif;
    text-shadow: 0 0 8px rgba(129, 140, 248, 0.4), 0 0 12px rgba(167, 139, 250, 0.3);
    transition: all 0.4s ease-in-out;
    cursor: pointer;
    /* 覆盖tailwind的定位样式，确保标题正常显示 */
    position: static;
    left: auto;
    top: auto;
    transform: none;
    max-width: none;
    pointer-events: auto;
}

.imax-text {
    font-size: 1.06em;
    margin: 0 6px;
}

.header-title:hover {
    filter: brightness(1.2);
    transform: scale(1.02);
}

.header-title.neon-active {
    background-image: none;
    -webkit-text-fill-color: white;
    animation: sharpNeonPulse 2s ease-in-out;
}

@keyframes sharpNeonPulse {
    50% {
        text-shadow:
            0 0 5px #fff,
            0 0 10px #fff,
            0 0 15px #fff,
            0 0 25px #a78bfa,
            0 0 45px #a78bfa,
            0 0 70px #818cf8;
    }
    0%, 100% {
         text-shadow:
            0 0 2px #fff,
            0 0 5px #a78bfa,
            0 0 10px #818cf8;
    }
}

/* 移动端专用样式 - 简化发光效果以解决Safari渲染问题 */
@media (max-width: 768px) {
    .header-title.neon-active {
        animation: mobileNeonPulse 2s ease-in-out;
    }

    @keyframes mobileNeonPulse {
        50% {
            text-shadow:
                0 0 8px #fff,
                0 0 15px #a78bfa;
        }
        0%, 100% {
            text-shadow:
                0 0 3px #fff,
                0 0 8px #a78bfa;
        }
    }
}

header {
    border-bottom: 1px solid;
    border-image-source: linear-gradient(to right, transparent, #4f46e5 50%, transparent);
    border-image-slice: 1;
}

/* 模式切换器样式 */
.mode-switcher {
    display: inline-flex;
    background-color: rgba(31, 41, 55, 0.7);
    border-radius: 0.5rem;
    padding: 0.25rem;
    border: 1px solid #374151;
}

.mode-btn {
    padding: 0.5rem 1.25rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-weight: 500;
    color: #9ca3af;
    border: none;
    background-color: transparent;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.mode-btn:hover {
    background-color: rgba(55, 65, 81, 0.5);
    color: #d1d5db;
}

.mode-btn.active {
    background-color: #4f46e5;
    color: #fff;
    box-shadow: 0 2px 10px rgba(79, 70, 229, 0.4);
}

.cinema-card {
    background-color: rgba(31, 41, 55, 0.5);
    backdrop-filter: blur(4px);
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    border: 1px solid #374151;
    transition: all 0.3s ease;
    position: relative;
    overflow: visible;
}

.cinema-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(139, 92, 246, 0.1), transparent);
    transition: left 0.5s ease;
}

@media (hover: hover) and (pointer: fine) {
    .cinema-card:hover {
        border-color: #6366f1;
        transform: translateY(-3px);
        box-shadow: 0 0 20px rgba(99, 102, 241, 0.2);
    }
    .cinema-card:hover::before {
        left: 100%;
    }
}

.cinema-name { 
    font-size: 1.125rem; 
    font-weight: 600; 
    color: #f9fafb; 
    margin-bottom: 0.75rem; 
}

.card-row { 
    display: flex; 
    justify-content: space-between; 
    align-items: center; 
    padding: 0.625rem 0; 
    border-bottom: 1px solid #374151; 
}

.card-row:last-child { 
    border-bottom: none; 
}

.card-label { 
    font-size: 0.875rem; 
    color: #9ca3af; 
}

.card-value { 
    font-size: 0.875rem; 
    color: #e5e7eb; 
    font-weight: 500; 
}

.cinema-type-badge { 
    display: inline-block; 
    padding: 0.375rem 0.75rem; 
    border-radius: 9999px; 
    font-size: 0.75rem; 
    font-weight: 600; 
    border: 1px solid transparent; 
    background-clip: padding-box; 
}

.audio-feature { 
    position: relative; 
    display: inline-flex; 
    align-items: center; 
    cursor: help; 
    user-select: none; 
    -webkit-user-select: none; 
    -ms-user-select: none; 
}

.audio-feature .tooltip-text { 
    visibility: hidden; 
    white-space: nowrap; 
    background-color: #111827; 
    color: #fff; 
    text-align: center; 
    border-radius: 6px; 
    padding: 8px 12px; 
    position: absolute; 
    z-index: 9999; 
    bottom: 150%; 
    left: 50%; 
    transform: translateX(-50%); 
    opacity: 0; 
    transition: opacity 0.3s; 
    font-size: 0.75rem; 
    pointer-events: none; 
    box-shadow: 0 4px 12px rgba(0,0,0,0.6); 
    border: 1px solid #4b5563; 
}

.audio-feature:hover .tooltip-text {
    visibility: visible;
    opacity: 1;
}

/* 移动端点击显示tooltip */
@media (max-width: 768px) {
    .audio-feature {
        cursor: pointer;
    }

    .tooltip-text.mobile-visible {
        visibility: visible !important;
        opacity: 1 !important;
    }
}

.stat-card {
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid #374151;
    background-color: rgba(31, 41, 55, 0.5);
    backdrop-filter: blur(4px);
    user-select: none;
    -webkit-user-select: none;
    -ms-user-select: none;
}

.stat-card:hover {
    border-color: #4f46e5;
    box-shadow: 0 0 15px rgba(79, 70, 229, 0.3);
}

.stat-card.active-filter {
    border-color: #818cf8;
    box-shadow: 0 0 20px rgba(129, 140, 248, 0.4);
}

.focus\:shadow-indigo:focus {
    box-shadow: 0 0 15px rgba(79, 70, 229, 0.3);
}

.loading-spinner {
    border: 4px solid #374151;
    border-top: 4px solid #6366f1;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@media (min-width: 768px) {
    #cinema-list-header { 
        display: grid; 
        grid-template-columns: 3fr 2fr 1fr 1fr 1fr 1fr; 
        gap: 1rem; 
        padding: 1rem 1.5rem; 
        background-color: transparent;
        font-weight: 600; 
        color: #9ca3af; 
        border-bottom: 2px solid #374151; 
    }
    
    .cinema-card { 
        display: grid; 
        grid-template-columns: 3fr 2fr 1fr 1fr 1fr 1fr; 
        gap: 1rem; 
        align-items: center; 
        padding: 1.25rem 1.5rem;
        margin-bottom: 0; 
        border-radius: 0; 
        border: none;
        border-bottom: 1px solid #374151; 
        box-shadow: none; 
        background-color: transparent;
        backdrop-filter: none;
        border-left: 3px solid transparent;
    }
    
    .cinema-card::before { 
        display: none; 
    }
    
    .cinema-card:hover { 
        background-color: #37415130; 
        transform: none; 
        box-shadow: none;
        border-left-color: #6366f1;
    }
    
    .cinema-card:last-child { 
        border-bottom: none; 
    }
    
    .card-row { 
        display: contents; 
    }
    
    .card-label { 
        display: none; 
    }
    
    .card-value { 
        color: #d1d5db; 
    }
    
    .cinema-name { 
        margin-bottom: 0; 
    }
    
    .sortable { 
        cursor: pointer; 
        transition: color 0.2s ease; 
        display: flex; 
        align-items: center; 
        user-select: none; 
        -webkit-user-select: none; 
        -ms-user-select: none; 
    }
    
    .sortable:hover, .sortable.active { 
        color: #f9fafb; 
    }
    
    .sort-icon-container { 
        margin-left: 0.5rem; 
        display: flex; 
        align-items: center; 
        height: 16px; 
        width: 16px; 
    }
    
    .sort-icon { 
        transition: all 0.3s ease; 
        color: #6366f1;
        opacity: 0;
        transform: translateY(3px);
    }
    
    .sortable.active .sort-icon-asc:not(.hidden),
    .sortable.active .sort-icon-desc:not(.hidden) {
         opacity: 1;
         transform: translateY(0);
    }
}
