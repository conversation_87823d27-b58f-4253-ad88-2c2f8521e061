using System;
using System.Collections.Generic;
using System.Web;
using System.Web.SessionState;
using Newtonsoft.Json;
using YaoHuo.Plugin.WebSite.Services.Config;

namespace YaoHuo.Plugin.Pages.IMAX
{
    /// <summary>
    /// IMAX影院数据API接口
    /// 提供安全的JSON数据访问，防止直接下载配置文件
    /// </summary>
    public class ImaxDataApi : IHttpHandler, IRequiresSessionState
    {
        public bool IsReusable => false;

        public void ProcessRequest(HttpContext context)
        {
            try
            {
                // 设置响应头
                context.Response.ContentType = "application/json";
                context.Response.Cache.SetCacheability(HttpCacheability.Public);
                context.Response.Cache.SetExpires(DateTime.Now.AddHours(1));
                context.Response.Cache.SetMaxAge(TimeSpan.FromHours(1));

                // 获取请求参数
                string action = context.Request.QueryString["action"] ?? "getCinemas";
                string type = context.Request.QueryString["type"] ?? "laser"; // 默认激光IMAX

                switch (action.ToLower())
                {
                    case "getcinemas":
                        GetCinemasData(context, type);
                        break;
                    case "getstats":
                        GetStatsData(context, type);
                        break;
                    default:
                        SendErrorResponse(context, "不支持的操作", 400);
                        break;
                }
            }
            catch (Exception)
            {
                SendErrorResponse(context, "服务器内部错误", 500);
            }
        }

        /// <summary>
        /// 获取影院数据
        /// </summary>
        /// <param name="context">HTTP上下文</param>
        /// <param name="type">影院类型：laser(激光IMAX) 或 digital(数字IMAX)</param>
        private void GetCinemasData(HttpContext context, string type)
        {
            try
            {
                List<ImaxCinema> cinemaData;

                if (type.ToLower() == "digital")
                {
                    // 读取数字IMAX配置
                    cinemaData = ConfigService.GetConfig<List<ImaxCinema>>("ImaxXenon", "IMAX");

                    // 处理数字IMAX的projectorType显示
                    if (cinemaData != null)
                    {
                        foreach (var cinema in cinemaData)
                        {
                            // 统一显示为"IMAX 氙灯"，不区分Gen 1、Gen 3等
                            if (!string.IsNullOrEmpty(cinema.projectorType) &&
                                (cinema.projectorType.Contains("Gen") || cinema.projectorType.Contains("氙灯")))
                            {
                                cinema.projectorType = "IMAX 氙灯";
                            }
                        }
                    }
                }
                else
                {
                    // 默认读取激光IMAX配置
                    cinemaData = ConfigService.GetConfig<List<ImaxCinema>>("ImaxCinemas", "IMAX");
                }

                if (cinemaData == null || cinemaData.Count == 0)
                {
                    SendErrorResponse(context, "暂无影院数据", 404);
                    return;
                }

                // 返回JSON数据
                string jsonResponse = JsonConvert.SerializeObject(cinemaData, Formatting.None);
                context.Response.Write(jsonResponse);
            }
            catch (Exception)
            {
                SendErrorResponse(context, "数据加载失败", 500);
            }
        }

        /// <summary>
        /// 获取统计数据
        /// </summary>
        /// <param name="context">HTTP上下文</param>
        /// <param name="type">影院类型：laser(激光IMAX) 或 digital(数字IMAX)</param>
        private void GetStatsData(HttpContext context, string type)
        {
            try
            {
                List<ImaxCinema> cinemaData;
                string configKey;

                if (type.ToLower() == "digital")
                {
                    cinemaData = ConfigService.GetConfig<List<ImaxCinema>>("ImaxXenon", "IMAX");
                    configKey = "ImaxXenon";
                }
                else
                {
                    cinemaData = ConfigService.GetConfig<List<ImaxCinema>>("ImaxCinemas", "IMAX");
                    configKey = "IMAX";
                }

                if (cinemaData == null)
                {
                    SendErrorResponse(context, "暂无数据", 404);
                    return;
                }

                object stats;

                if (type.ToLower() == "digital")
                {
                    // 数字IMAX按银幕面积分类统计
                    stats = new
                    {
                        totalCount = cinemaData.Count,
                        gt300Count = cinemaData.FindAll(c => {
                            if (double.TryParse(c.screenArea, out double area))
                                return area > 300;
                            return false;
                        }).Count,
                        between200And300Count = cinemaData.FindAll(c => {
                            if (double.TryParse(c.screenArea, out double area))
                                return area > 200 && area <= 300;
                            return false;
                        }).Count,
                        lt200Count = cinemaData.FindAll(c => {
                            if (double.TryParse(c.screenArea, out double area))
                                return area <= 200 && area > 0;
                            return false;
                        }).Count,
                        lastUpdated = ConfigService.GetConfigLastModified("ImaxXenon", "IMAX")?.ToString("yyyy-MM-dd HH:mm:ss") ?? "未知"
                    };
                }
                else
                {
                    // 激光IMAX按放映机类型统计
                    stats = new
                    {
                        totalCount = cinemaData.Count,
                        gtCount = cinemaData.FindAll(c => (c.projectorType ?? "").Contains("GT")).Count,
                        commercialCount = cinemaData.FindAll(c => (c.projectorType ?? "").Contains("Commercial")).Count,
                        laserXTCount = cinemaData.FindAll(c => (c.projectorType ?? "").Contains("Laser XT")).Count,
                        lastUpdated = ConfigService.GetConfigLastModified("ImaxCinemas", "IMAX")?.ToString("yyyy-MM-dd HH:mm:ss") ?? "未知"
                    };
                }

                string jsonResponse = JsonConvert.SerializeObject(stats, Formatting.None);
                context.Response.Write(jsonResponse);
            }
            catch (Exception)
            {
                SendErrorResponse(context, "统计数据获取失败", 500);
            }
        }

        /// <summary>
        /// 发送错误响应
        /// </summary>
        private void SendErrorResponse(HttpContext context, string message, int statusCode)
        {
            context.Response.StatusCode = statusCode;
            var errorResponse = new { error = true, message = message };
            string jsonResponse = JsonConvert.SerializeObject(errorResponse, Formatting.None);
            context.Response.Write(jsonResponse);
        }
    }

    /// <summary>
    /// IMAX影院数据模型
    /// </summary>
    public class ImaxCinema
    {
        public string name { get; set; }
        public string projectorType { get; set; }
        public string audioSystem { get; set; }
        public string screenArea { get; set; }
        public string screenWidth { get; set; }
        public string screenHeight { get; set; }
        public string seatCount { get; set; }
        public string openDate { get; set; }
        public string remarks { get; set; }
    }
}