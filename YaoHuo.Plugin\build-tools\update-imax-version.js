const fs = require('fs');
const path = require('path');

const imaxPagePath = path.resolve(__dirname, '../Pages/IMAX/IMAX.aspx');

try {
    let pageContent = fs.readFileSync(imaxPagePath, 'utf8');
    const newVersion = Date.now(); // 使用当前时间戳作为新的版本号

    // 更新CSS版本号
    const updatedContent = pageContent.replace(
        /href=\"(\/Pages\/IMAX\/IMAX\.css)\?v[\d.]+\"/,
        `href=\"$1?v${newVersion}\"`
    );

    fs.writeFileSync(imaxPagePath, updatedContent, 'utf8');
    console.log(`Updated IMAX.aspx with new CSS version: v${newVersion}`);
} catch (error) {
    console.error(`Error updating CSS version in IMAX.aspx: ${error.message}`);
}
