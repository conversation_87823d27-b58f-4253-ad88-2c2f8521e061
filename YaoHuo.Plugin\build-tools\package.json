{"name": "yaohuo.plugin", "version": "1.0.0", "main": "index.js", "scripts": {"build:tailwind": "tailwindcss -i ./style.css -o ../Template/CSS/output.css --minify", "postbuild:tailwind": "node update-css-version.js", "build:imax": "tailwindcss -i ./style.css -o ../Pages/IMAX/tailwind.min.css --minify", "postbuild:imax": "node update-imax-version.js", "watch:tailwind": "tailwindcss -i ./style.css -o ../Template/CSS/output.css --watch", "watch:with-version": "node watch-with-version.js", "build:ts": "tsc --project ./typescript/tsconfig.json", "postbuild:ts": "node update-js-version.js", "watch:ts": "tsc --project ./typescript/tsconfig.json --watch", "build:all": "npm run build:ts && npm run build:tailwind && npm run build:imax", "watch:all": "concurrently \"npm run watch:ts\" \"npm run watch:tailwind\"", "dev": "npm run watch:all", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "devDependencies": {"@types/node": "^24.0.7", "autoprefixer": "^10.4.21", "concurrently": "^9.2.0", "glob": "^11.0.3", "postcss": "^8.5.4", "tailwindcss": "^4.1.8", "typescript": "^5.8.3"}}