@tailwind base;
@tailwind components;
@tailwind utilities;

/* IMAX页面专用样式 - 只包含必要的组件 */
@layer components {
    /* 模式切换器 */
    .mode-switcher {
        @apply flex justify-center mb-8;
    }
    
    .mode-btn {
        @apply px-6 py-2 mx-2 rounded-lg font-medium transition-all duration-300;
    }
    
    .mode-btn.active {
        @apply bg-white text-gray-800 shadow-lg;
    }
    
    .mode-btn:not(.active) {
        @apply text-gray-300 hover:text-white hover:bg-white hover:bg-opacity-10;
    }
    
    /* 搜索容器 */
    .search-container {
        @apply max-w-md mx-auto mb-8;
    }
    
    .search-input {
        @apply w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-indigo-500 focus:ring-1 focus:ring-indigo-500;
    }
    
    /* 排序容器 */
    .sort-container {
        @apply flex justify-center mb-6;
    }
    
    .sort-btn {
        @apply px-4 py-2 mx-1 rounded-md text-sm font-medium transition-all duration-200;
    }
    
    .sort-btn.active {
        @apply bg-indigo-600 text-white;
    }
    
    .sort-btn:not(.active) {
        @apply text-gray-400 hover:text-white hover:bg-gray-700;
    }
    
    /* 影院卡片 */
    .cinema-card {
        @apply bg-gray-800 rounded-lg p-6 mb-4 border border-gray-700 hover:border-indigo-500 transition-all duration-300;
    }
    
    .cinema-name {
        @apply text-xl font-bold text-white mb-2;
    }
    
    .cinema-location {
        @apply text-gray-400 mb-3;
    }
    
    .cinema-specs {
        @apply grid grid-cols-2 gap-4 text-sm;
    }
    
    .spec-item {
        @apply flex justify-between;
    }
    
    .spec-label {
        @apply text-gray-400;
    }
    
    .spec-value {
        @apply text-white font-medium;
    }
    
    /* 投影仪类型标签 */
    .projector-tag {
        @apply inline-block px-3 py-1 rounded-full text-xs font-medium;
    }
    
    .projector-gt {
        @apply bg-green-600 text-white;
    }
    
    .projector-commercial {
        @apply bg-blue-600 text-white;
    }
    
    .projector-xt {
        @apply bg-purple-600 text-white;
    }
    
    .projector-digital {
        @apply bg-gray-600 text-white;
    }
    
    /* 屏幕尺寸标签 */
    .screen-size-large {
        @apply text-green-400;
    }
    
    .screen-size-medium {
        @apply text-yellow-400;
    }
    
    .screen-size-small {
        @apply text-orange-400;
    }
    
    /* 音响系统图标 */
    .audio-icon {
        @apply text-yellow-400;
    }
    
    /* 响应式调整 */
    @media (max-width: 768px) {
        .cinema-specs {
            @apply grid-cols-1 gap-2;
        }
        
        .mode-btn {
            @apply px-4 py-2 mx-1 text-sm;
        }
        
        .cinema-card {
            @apply p-4;
        }
        
        .cinema-name {
            @apply text-lg;
        }
    }
}
